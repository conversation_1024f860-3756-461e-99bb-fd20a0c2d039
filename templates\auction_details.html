<!DOCTYPE html>
<html>
<head>
    <title>Auction {{ auction.auction_id }} - {{ auction.title[:50] }} - Aurena Tracker</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .lot-card {
            transition: transform 0.2s ease;
        }
        .lot-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .status-active { color: #198754; }
        .status-closed { color: #6c757d; }
        .price-display {
            font-size: 1.2rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-gavel me-2"></i>
                Aurena Auction Tracker
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Dashboard</a>
                <a class="nav-link" href="/timetable">Timetable</a>
                <a class="nav-link" href="/auction-tree">Auction Tree</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
                <li class="breadcrumb-item active">Auction {{ auction.auction_id }}</li>
            </ol>
        </nav>

        <!-- Auction Header -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3><i class="fas fa-gavel me-2"></i>Auction {{ auction.auction_id }}</h3>
                        <h5 class="text-muted">{{ auction.title }}</h5>
                    </div>
                    <div class="col-md-4 text-end">
                        <span class="badge bg-{{ 'success' if auction.status == 'active' else 'secondary' }} fs-6">
                            {{ auction.status.title() }}
                        </span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>Start Time:</strong><br>
                        <span class="text-muted">{{ auction.start_time.strftime('%Y-%m-%d %H:%M') if auction.start_time else 'Unknown' }}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>End Time:</strong><br>
                        <span class="text-muted">{{ auction.end_time.strftime('%Y-%m-%d %H:%M') if auction.end_time else 'Unknown' }}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>Total Lots:</strong><br>
                        <span class="text-muted">{{ auction.lot_count or 0 }}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>Location:</strong><br>
                        <span class="text-muted">{{ auction.location or 'Not specified' }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lots List -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-boxes me-2"></i>Auction Lots ({{ lots|length }})</h5>
                <div class="btn-group" role="group">
                    <button class="btn btn-sm btn-outline-primary" onclick="sortLots('number')">Sort by Number</button>
                    <button class="btn btn-sm btn-outline-primary" onclick="sortLots('price')">Sort by Price</button>
                    <button class="btn btn-sm btn-outline-primary" onclick="sortLots('closing')">Sort by Closing</button>
                </div>
            </div>
            <div class="card-body">
                {% if lots %}
                    <div class="row" id="lots-container">
                        {% for lot in lots %}
                        <div class="col-lg-6 col-xl-4 mb-3 lot-item" 
                             data-number="{{ lot.lot_number or 0 }}" 
                             data-price="{{ lot.current_price or 0 }}" 
                             data-closing="{{ lot.closing_time.isoformat() if lot.closing_time else '' }}">
                            <div class="card lot-card h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title">
                                            <a href="/lot/{{ lot.lot_id }}" class="text-decoration-none">
                                                Lot {{ lot.lot_number or 'N/A' }}
                                            </a>
                                        </h6>
                                        <span class="badge bg-{{ 'success' if lot.status == 'active' else 'secondary' }}">
                                            {{ lot.status.title() if lot.status else 'Unknown' }}
                                        </span>
                                    </div>
                                    
                                    <p class="card-text small text-muted">
                                        {{ lot.title[:60] }}{% if lot.title and lot.title|length > 60 %}...{% endif %}
                                    </p>
                                    
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="price-display text-success">
                                            €{{ lot.current_price or 0 }}
                                        </div>
                                        <small class="text-muted">
                                            {% if lot.closing_time %}
                                                <i class="fas fa-clock me-1"></i>
                                                {{ lot.closing_time.strftime('%H:%M') }}
                                            {% else %}
                                                No closing time
                                            {% endif %}
                                        </small>
                                    </div>
                                    
                                    {% if lot.bid_count and lot.bid_count > 0 %}
                                    <small class="text-muted">
                                        <i class="fas fa-users me-1"></i>{{ lot.bid_count }} bids
                                    </small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No lots found for this auction</h5>
                        <p class="text-muted">This auction may not have any lots yet, or they may not be loaded.</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <a href="/" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
                <a href="/timetable" class="btn btn-outline-info">
                    <i class="fas fa-clock me-2"></i>View Timetable
                </a>
                <button class="btn btn-primary" onclick="window.open('https://www.aurena.at/auktion/{{ auction.auction_id }}', '_blank')">
                    <i class="fas fa-external-link-alt me-2"></i>View on Aurena
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function sortLots(criteria) {
            const container = document.getElementById('lots-container');
            const lots = Array.from(container.children);
            
            lots.sort((a, b) => {
                let aVal, bVal;
                
                switch(criteria) {
                    case 'number':
                        aVal = parseInt(a.dataset.number) || 0;
                        bVal = parseInt(b.dataset.number) || 0;
                        return aVal - bVal;
                    case 'price':
                        aVal = parseFloat(a.dataset.price) || 0;
                        bVal = parseFloat(b.dataset.price) || 0;
                        return bVal - aVal; // Descending
                    case 'closing':
                        aVal = new Date(a.dataset.closing || '1970-01-01');
                        bVal = new Date(b.dataset.closing || '1970-01-01');
                        return aVal - bVal;
                    default:
                        return 0;
                }
            });
            
            // Re-append sorted elements
            lots.forEach(lot => container.appendChild(lot));
        }
        
        // Auto-refresh every 60 seconds
        setTimeout(function() {
            location.reload();
        }, 60000);
    </script>
</body>
</html>
