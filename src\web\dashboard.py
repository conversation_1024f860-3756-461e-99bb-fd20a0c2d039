#!/usr/bin/env python3
"""
Web Dashboard for Auction Scraper System
Provides comprehensive web interface with tree navigation, inspection capabilities, and timetable.
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import json

from ..database.manager import DatabaseManager
from ..scheduler.auction_scheduler import AuctionScheduler

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AuctionDashboard:
    """Main web dashboard application"""
    
    def __init__(self, db_path: str = "data/auction_scraper.db"):
        self.app = Flask(__name__, template_folder='templates', static_folder='static')
        self.db_manager = DatabaseManager(db_path)
        self.scheduler = AuctionScheduler(self.db_manager)
        
        # Configure Flask
        self.app.config['SECRET_KEY'] = 'auction-scraper-secret-key'
        self.app.config['JSON_SORT_KEYS'] = False
        
        # Register routes
        self._register_routes()
        
        # Start scheduler
        self.scheduler.start()
    
    def _register_routes(self):
        """Register all Flask routes"""
        
        @self.app.route('/')
        def dashboard():
            """Main dashboard page"""
            try:
                # Get statistics
                stats = self.db_manager.get_statistics()
                
                # Get recent auctions
                recent_auctions = self.db_manager.get_auctions(limit=10)
                
                # Get upcoming lots
                upcoming_lots = self.db_manager.get_upcoming_lots(hours=48)
                
                # Get auction tree for navigation
                auction_tree = self.db_manager.get_auction_tree()
                
                # Get recent lifecycle logs
                recent_logs = self.db_manager.get_lifecycle_logs(limit=20)
                
                return render_template('dashboard.html',
                                     stats=stats,
                                     recent_auctions=recent_auctions,
                                     upcoming_lots=upcoming_lots,
                                     auction_tree=auction_tree,
                                     recent_logs=recent_logs)
            except Exception as e:
                logger.error(f"Error loading dashboard: {e}")
                return f"Error loading dashboard: {e}", 500
        
        @self.app.route('/auctions')
        def auctions_list():
            """Auctions list page"""
            try:
                auctions = self.db_manager.get_auctions(limit=100)
                return render_template('auctions.html', auctions=auctions)
            except Exception as e:
                logger.error(f"Error loading auctions: {e}")
                return f"Error loading auctions: {e}", 500
        
        @self.app.route('/auction/<int:auction_id>')
        def auction_details(auction_id: int):
            """Auction details page"""
            try:
                auction = self.db_manager.get_auction(auction_id)
                if not auction:
                    return "Auction not found", 404
                
                lots = self.db_manager.get_lots_by_auction(auction_id)
                
                return render_template('auction_details.html',
                                     auction=auction,
                                     lots=lots)
            except Exception as e:
                logger.error(f"Error loading auction {auction_id}: {e}")
                return f"Error loading auction: {e}", 500
        
        @self.app.route('/lot/<int:lot_id>')
        def lot_details(lot_id: int):
            """Lot details page"""
            try:
                lot = self.db_manager.get_lot(lot_id)
                if not lot:
                    return "Lot not found", 404
                
                auction = self.db_manager.get_auction(lot.auction_id)
                bids = self.db_manager.get_bids_for_lot(lot_id)
                
                return render_template('lot_details.html',
                                     lot=lot,
                                     auction=auction,
                                     bids=bids)
            except Exception as e:
                logger.error(f"Error loading lot {lot_id}: {e}")
                return f"Error loading lot: {e}", 500
        
        @self.app.route('/timetable')
        def timetable():
            """Timetable page showing closing times and statuses"""
            try:
                # Get upcoming lots for next 7 days
                upcoming_lots = self.db_manager.get_upcoming_lots(hours=168)
                
                # Group by date
                timetable_data = {}
                for lot in upcoming_lots:
                    if lot.closing_time:
                        date_key = lot.closing_time.strftime('%Y-%m-%d')
                        if date_key not in timetable_data:
                            timetable_data[date_key] = []
                        
                        # Determine status
                        now = datetime.now()
                        if lot.closing_time < now:
                            status = "Closed"
                        elif lot.closing_time < now + timedelta(minutes=5):
                            status = "Closing Soon"
                        elif lot.closing_time < now + timedelta(hours=1):
                            status = "Closing Today"
                        else:
                            status = "Waiting"
                        
                        timetable_data[date_key].append({
                            'lot': lot,
                            'status': status
                        })
                
                # Sort dates
                sorted_dates = sorted(timetable_data.keys())
                
                return render_template('timetable.html',
                                     timetable_data=timetable_data,
                                     sorted_dates=sorted_dates)
            except Exception as e:
                logger.error(f"Error loading timetable: {e}")
                return f"Error loading timetable: {e}", 500
        
        @self.app.route('/auction-tree')
        def auction_tree():
            """Auction tree navigation page"""
            try:
                tree_data = self.db_manager.get_auction_tree()
                return render_template('auction_tree.html', tree_data=tree_data)
            except Exception as e:
                logger.error(f"Error loading auction tree: {e}")
                return f"Error loading auction tree: {e}", 500
        
        @self.app.route('/scraping-control')
        def scraping_control():
            """Scraping control dashboard"""
            try:
                # Get scheduled tasks
                scheduled_tasks = self.scheduler.get_scheduled_tasks()
                
                # Get recent lifecycle logs
                recent_logs = self.db_manager.get_lifecycle_logs(limit=50)
                
                # Get statistics by lifecycle stage
                stats = self.db_manager.get_statistics()
                
                return render_template('scraping_control.html',
                                     scheduled_tasks=scheduled_tasks,
                                     recent_logs=recent_logs,
                                     stats=stats)
            except Exception as e:
                logger.error(f"Error loading scraping control: {e}")
                return f"Error loading scraping control: {e}", 500
        
        # API Routes
        @self.app.route('/api/discover-auctions', methods=['POST'])
        def api_discover_auctions():
            """API endpoint to trigger auction discovery"""
            try:
                success = self.scheduler.trigger_manual_auction_discovery()
                return jsonify({
                    'success': success,
                    'message': 'Auction discovery triggered' if success else 'Failed to trigger discovery'
                })
            except Exception as e:
                logger.error(f"Error triggering auction discovery: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500
        
        @self.app.route('/api/discover-lots/<int:auction_id>', methods=['POST'])
        def api_discover_lots(auction_id: int):
            """API endpoint to trigger lot discovery for an auction"""
            try:
                success = self.scheduler.trigger_manual_lot_discovery(auction_id)
                return jsonify({
                    'success': success,
                    'message': f'Lot discovery triggered for auction {auction_id}' if success else 'Failed to trigger discovery'
                })
            except Exception as e:
                logger.error(f"Error triggering lot discovery: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500
        
        @self.app.route('/api/collect-bids/<int:lot_id>', methods=['POST'])
        def api_collect_bids(lot_id: int):
            """API endpoint to trigger bid collection for a lot"""
            try:
                success = self.scheduler.trigger_manual_bid_collection(lot_id)
                return jsonify({
                    'success': success,
                    'message': f'Bid collection triggered for lot {lot_id}' if success else 'Failed to trigger collection'
                })
            except Exception as e:
                logger.error(f"Error triggering bid collection: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500
        
        @self.app.route('/api/stats')
        def api_stats():
            """API endpoint to get current statistics"""
            try:
                stats = self.db_manager.get_statistics()
                return jsonify(stats)
            except Exception as e:
                logger.error(f"Error getting stats: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/scheduled-tasks')
        def api_scheduled_tasks():
            """API endpoint to get scheduled tasks"""
            try:
                tasks = self.scheduler.get_scheduled_tasks()
                return jsonify(tasks)
            except Exception as e:
                logger.error(f"Error getting scheduled tasks: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/lifecycle-logs')
        def api_lifecycle_logs():
            """API endpoint to get lifecycle logs"""
            try:
                limit = request.args.get('limit', 50, type=int)
                logs = self.db_manager.get_lifecycle_logs(limit=limit)
                
                # Convert to dict for JSON serialization
                logs_data = [log.to_dict() for log in logs]
                return jsonify(logs_data)
            except Exception as e:
                logger.error(f"Error getting lifecycle logs: {e}")
                return jsonify({'error': str(e)}), 500
        
        # Error handlers
        @self.app.errorhandler(404)
        def not_found(error):
            return render_template('error.html', 
                                 error_code=404, 
                                 error_message="Page not found"), 404
        
        @self.app.errorhandler(500)
        def internal_error(error):
            return render_template('error.html', 
                                 error_code=500, 
                                 error_message="Internal server error"), 500
    
    def run(self, host='0.0.0.0', port=5000, debug=False):
        """Run the Flask application"""
        logger.info(f"🌐 Starting auction scraper dashboard on http://{host}:{port}")
        self.app.run(host=host, port=port, debug=debug)
    
    def stop(self):
        """Stop the dashboard and scheduler"""
        self.scheduler.stop()
        logger.info("🛑 Dashboard stopped")


# Template filters
def create_app(db_path: str = "data/auction_scraper.db") -> Flask:
    """Create Flask application instance"""
    dashboard = AuctionDashboard(db_path)
    
    # Add custom template filters
    @dashboard.app.template_filter('datetime_format')
    def datetime_format(value, format='%Y-%m-%d %H:%M:%S'):
        """Format datetime for templates"""
        if value is None:
            return ""
        if isinstance(value, str):
            try:
                value = datetime.fromisoformat(value)
            except:
                return value
        return value.strftime(format)
    
    @dashboard.app.template_filter('time_ago')
    def time_ago(value):
        """Show time ago for templates"""
        if value is None:
            return ""
        if isinstance(value, str):
            try:
                value = datetime.fromisoformat(value)
            except:
                return value
        
        now = datetime.now()
        diff = now - value
        
        if diff.days > 0:
            return f"{diff.days} days ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hours ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes} minutes ago"
        else:
            return "Just now"
    
    @dashboard.app.template_filter('currency')
    def currency_format(value):
        """Format currency for templates"""
        if value is None or value == 0:
            return "€0"
        return f"€{value:,}"
    
    @dashboard.app.template_filter('status_badge')
    def status_badge(status):
        """Get Bootstrap badge class for status"""
        status_classes = {
            'active': 'bg-success',
            'closed': 'bg-secondary',
            'sold': 'bg-primary',
            'withdrawn': 'bg-warning',
            'pending': 'bg-info',
            'running': 'bg-primary',
            'completed': 'bg-success',
            'failed': 'bg-danger',
            'discovered': 'bg-info',
            'lots_discovered': 'bg-warning',
            'bids_collected': 'bg-success',
            'extension_detected': 'bg-warning'
        }
        return status_classes.get(status, 'bg-secondary')
    
    return dashboard.app


if __name__ == "__main__":
    dashboard = AuctionDashboard()
    dashboard.run(debug=True)
