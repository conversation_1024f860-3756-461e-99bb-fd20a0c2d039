#!/usr/bin/env python3
"""
Lot Details Scraper
Scrapes detailed information for individual lots including offer/bid history.
Implements precise timing for bid collection 5 seconds before/after closing.
"""

import requests
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from bs4 import BeautifulSoup
import json
import re
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LotDetailsScraper:
    """Scraper for extracting detailed lot information and bid history"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.base_url = "https://www.aurena.at"
        
    def scrape_lot_details(self, lot_id: int, include_bids: bool = True) -> Dict:
        """
        Main method to scrape detailed information for a specific lot.
        Returns dictionary with lot details and optionally bid history.
        """
        logger.info(f"🔍 Scraping details for lot {lot_id}...")
        
        # Get basic lot details
        lot_details = self._extract_lot_details(lot_id)
        
        if not lot_details:
            logger.error(f"Could not extract details for lot {lot_id}")
            return {}
        
        # Get bid history if requested
        if include_bids:
            bids = self._extract_bid_history(lot_id)
            lot_details['bids'] = bids
            lot_details['bid_count'] = len(bids)
        
        logger.info(f"✅ Extracted details for lot {lot_id}")
        return lot_details
    
    def scrape_lot_bids_timed(self, lot_id: int, closing_time: datetime) -> List[Dict]:
        """
        Scrape lot bids with precise timing around closing time.
        Fetches bids 5 seconds before and after closing, with retries.
        """
        logger.info(f"⏰ Starting timed bid collection for lot {lot_id}")
        
        all_bids = []
        
        # Calculate timing windows
        pre_closing = closing_time - timedelta(seconds=5)
        post_closing = closing_time + timedelta(seconds=5)
        
        # Wait until pre-closing window
        now = datetime.now()
        if now < pre_closing:
            wait_seconds = (pre_closing - now).total_seconds()
            logger.info(f"Waiting {wait_seconds:.1f} seconds until pre-closing window...")
            time.sleep(wait_seconds)
        
        # Collect bids in pre-closing window
        logger.info("📊 Collecting pre-closing bids...")
        pre_bids = self._extract_bid_history(lot_id)
        all_bids.extend(pre_bids)
        
        # Wait until post-closing window
        now = datetime.now()
        if now < post_closing:
            wait_seconds = (post_closing - now).total_seconds()
            logger.info(f"Waiting {wait_seconds:.1f} seconds until post-closing window...")
            time.sleep(wait_seconds)
        
        # Collect bids in post-closing window with retries
        logger.info("📊 Collecting post-closing bids...")
        max_retries = 3
        for attempt in range(max_retries):
            try:
                post_bids = self._extract_bid_history(lot_id)
                
                # Check if we got new bids
                new_bids = [bid for bid in post_bids if bid not in all_bids]
                all_bids.extend(new_bids)
                
                if new_bids or attempt == max_retries - 1:
                    break
                    
                logger.info(f"Retry {attempt + 1}/{max_retries} in 2 seconds...")
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"Error in bid collection attempt {attempt + 1}: {e}")
                if attempt == max_retries - 1:
                    break
                time.sleep(2)
        
        # Remove duplicates and sort by timestamp
        unique_bids = self._remove_duplicate_bids(all_bids)
        sorted_bids = sorted(unique_bids, key=lambda x: x.get('timestamp', datetime.min))
        
        logger.info(f"✅ Collected {len(sorted_bids)} unique bids for lot {lot_id}")
        return sorted_bids
    
    def _extract_lot_details(self, lot_id: int) -> Optional[Dict]:
        """Extract detailed lot information from lot page"""
        try:
            url = f"{self.base_url}/lot/{lot_id}"
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract basic details
            details = {
                'lot_id': lot_id,
                'title': self._extract_title(soup),
                'description': self._extract_description(soup),
                'current_price': self._extract_current_price(soup),
                'starting_price': self._extract_starting_price(soup),
                'closing_time': self._extract_closing_time(soup),
                'images': self._extract_images(soup),
                'status': self._extract_status(soup),
                'location': self._extract_location(soup),
                'condition': self._extract_condition(soup),
                'category': self._extract_category(soup)
            }
            
            return details
            
        except Exception as e:
            logger.error(f"Error extracting lot details for {lot_id}: {e}")
            return None
    
    def _extract_bid_history(self, lot_id: int) -> List[Dict]:
        """Extract bid history for a lot"""
        try:
            # Method 1: Try direct bid history page
            bids = self._extract_bids_from_history_page(lot_id)
            if bids:
                return bids
            
            # Method 2: Extract from main lot page
            bids = self._extract_bids_from_lot_page(lot_id)
            if bids:
                return bids
            
            # Method 3: Try API endpoints
            bids = self._extract_bids_from_api(lot_id)
            return bids
            
        except Exception as e:
            logger.error(f"Error extracting bid history for lot {lot_id}: {e}")
            return []
    
    def _extract_bids_from_history_page(self, lot_id: int) -> List[Dict]:
        """Extract bids from dedicated bid history page"""
        try:
            url = f"{self.base_url}/lot/{lot_id}/bids"
            response = self.session.get(url, timeout=15)
            
            if response.status_code != 200:
                return []
            
            soup = BeautifulSoup(response.content, 'html.parser')
            bids = []
            
            # Look for bid table or list
            bid_rows = soup.find_all(['tr', 'div'], class_=re.compile(r'bid|offer'))
            
            for row in bid_rows:
                bid_data = self._parse_bid_element(row, lot_id)
                if bid_data:
                    bids.append(bid_data)
            
            return bids
            
        except Exception as e:
            logger.debug(f"Error extracting bids from history page: {e}")
            return []
    
    def _extract_bids_from_lot_page(self, lot_id: int) -> List[Dict]:
        """Extract bids from main lot page"""
        try:
            url = f"{self.base_url}/lot/{lot_id}"
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            bids = []
            
            # Look for embedded bid data in JavaScript
            scripts = soup.find_all('script')
            for script in scripts:
                if not script.string:
                    continue
                
                # Look for bid data patterns
                bid_patterns = [
                    r'bids?\s*[:=]\s*(\[.*?\])',
                    r'bidHistory\s*[:=]\s*(\[.*?\])',
                    r'offers?\s*[:=]\s*(\[.*?\])',
                    r'"bids":\s*(\[.*?\])'
                ]
                
                for pattern in bid_patterns:
                    matches = re.finditer(pattern, script.string, re.DOTALL | re.IGNORECASE)
                    for match in matches:
                        try:
                            json_str = match.group(1)
                            bid_array = json.loads(json_str)
                            for bid_item in bid_array:
                                bid_data = self._parse_bid_json(bid_item, lot_id)
                                if bid_data:
                                    bids.append(bid_data)
                        except json.JSONDecodeError:
                            continue
            
            return bids
            
        except Exception as e:
            logger.debug(f"Error extracting bids from lot page: {e}")
            return []
    
    def _extract_bids_from_api(self, lot_id: int) -> List[Dict]:
        """Extract bids from API endpoints"""
        try:
            api_endpoints = [
                f"/api/lot/{lot_id}/bids",
                f"/api/bids?lot={lot_id}",
                f"/ajax/lot/{lot_id}/bids",
                f"/api/v1/lot/{lot_id}/bids"
            ]
            
            for endpoint in api_endpoints:
                try:
                    url = f"{self.base_url}{endpoint}"
                    response = self.session.get(url, timeout=15)
                    
                    if response.status_code == 200:
                        data = response.json()
                        bids = []
                        
                        if isinstance(data, list):
                            for item in data:
                                bid_data = self._parse_bid_json(item, lot_id)
                                if bid_data:
                                    bids.append(bid_data)
                        elif isinstance(data, dict) and 'bids' in data:
                            for item in data['bids']:
                                bid_data = self._parse_bid_json(item, lot_id)
                                if bid_data:
                                    bids.append(bid_data)
                        
                        if bids:
                            return bids
                            
                except Exception as e:
                    logger.debug(f"API endpoint {endpoint} failed: {e}")
                    continue
            
            return []
            
        except Exception as e:
            logger.debug(f"Error extracting bids from API: {e}")
            return []
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """Extract lot title"""
        title_selectors = ['h1', '.lot-title', '.title', '[class*="title"]']
        for selector in title_selectors:
            elem = soup.select_one(selector)
            if elem:
                return elem.get_text(strip=True)
        return "Unknown Lot"
    
    def _extract_description(self, soup: BeautifulSoup) -> str:
        """Extract lot description"""
        desc_selectors = ['.description', '.lot-description', '[class*="desc"]']
        for selector in desc_selectors:
            elem = soup.select_one(selector)
            if elem:
                return elem.get_text(strip=True)
        return ""
    
    def _extract_current_price(self, soup: BeautifulSoup) -> int:
        """Extract current price"""
        price_text = soup.find(text=re.compile(r'€\s*\d+|\d+\s*€'))
        if price_text:
            price_match = re.search(r'(\d+)', price_text)
            if price_match:
                return int(price_match.group(1))
        return 0
    
    def _extract_starting_price(self, soup: BeautifulSoup) -> int:
        """Extract starting price"""
        # Look for starting price indicators
        starting_elem = soup.find(text=re.compile(r'starting|start|reserve', re.IGNORECASE))
        if starting_elem:
            parent = starting_elem.parent
            price_text = parent.find(text=re.compile(r'€\s*\d+|\d+\s*€'))
            if price_text:
                price_match = re.search(r'(\d+)', price_text)
                if price_match:
                    return int(price_match.group(1))
        return 0
    
    def _extract_closing_time(self, soup: BeautifulSoup) -> Optional[datetime]:
        """Extract closing time"""
        # Look for closing time in various formats
        time_patterns = [
            r'(\d{1,2}[./]\d{1,2}[./]\d{2,4}\s+\d{1,2}:\d{2})',
            r'closes?\s+(\d{1,2}[./]\d{1,2}[./]\d{2,4})',
            r'ending?\s+(\d{1,2}[./]\d{1,2}[./]\d{2,4})'
        ]
        
        page_text = soup.get_text()
        for pattern in time_patterns:
            match = re.search(pattern, page_text, re.IGNORECASE)
            if match:
                try:
                    # Basic date parsing - would need enhancement for production
                    return datetime.now() + timedelta(hours=24)  # Placeholder
                except:
                    continue
        
        return None
    
    def _extract_images(self, soup: BeautifulSoup) -> List[str]:
        """Extract image URLs"""
        images = []
        img_tags = soup.find_all('img')
        for img in img_tags:
            src = img.get('src') or img.get('data-src')
            if src and ('lot' in src.lower() or 'auction' in src.lower()):
                if src.startswith('/'):
                    src = self.base_url + src
                images.append(src)
        return images
    
    def _extract_status(self, soup: BeautifulSoup) -> str:
        """Extract lot status"""
        status_indicators = ['active', 'closed', 'sold', 'withdrawn']
        page_text = soup.get_text().lower()
        
        for status in status_indicators:
            if status in page_text:
                return status
        
        return 'active'
    
    def _extract_location(self, soup: BeautifulSoup) -> str:
        """Extract lot location"""
        location_elem = soup.find(text=re.compile(r'location|address', re.IGNORECASE))
        if location_elem and location_elem.parent:
            return location_elem.parent.get_text(strip=True)
        return ""
    
    def _extract_condition(self, soup: BeautifulSoup) -> str:
        """Extract lot condition"""
        condition_elem = soup.find(text=re.compile(r'condition|state', re.IGNORECASE))
        if condition_elem and condition_elem.parent:
            return condition_elem.parent.get_text(strip=True)
        return ""
    
    def _extract_category(self, soup: BeautifulSoup) -> str:
        """Extract lot category"""
        category_elem = soup.find(text=re.compile(r'category|type', re.IGNORECASE))
        if category_elem and category_elem.parent:
            return category_elem.parent.get_text(strip=True)
        return ""
    
    def _parse_bid_element(self, element, lot_id: int) -> Optional[Dict]:
        """Parse bid data from HTML element"""
        try:
            text = element.get_text()
            
            # Extract amount
            amount_match = re.search(r'€\s*(\d+)|\$\s*(\d+)|(\d+)\s*€', text)
            if not amount_match:
                return None
            
            amount = int(amount_match.group(1) or amount_match.group(2) or amount_match.group(3))
            
            # Extract timestamp (basic pattern)
            time_match = re.search(r'(\d{1,2}:\d{2})', text)
            timestamp = datetime.now()  # Placeholder - would need proper parsing
            
            return {
                'lot_id': lot_id,
                'amount': amount,
                'timestamp': timestamp,
                'bid_time': timestamp.strftime('%H:%M:%S'),
                'bid_date': timestamp.strftime('%Y-%m-%d')
            }
            
        except Exception as e:
            logger.debug(f"Error parsing bid element: {e}")
            return None
    
    def _parse_bid_json(self, bid_item: Dict, lot_id: int) -> Optional[Dict]:
        """Parse bid data from JSON object"""
        try:
            amount_fields = ['amount', 'price', 'bid', 'value']
            time_fields = ['timestamp', 'time', 'date', 'created']
            
            # Extract amount
            amount = None
            for field in amount_fields:
                if field in bid_item and bid_item[field]:
                    amount = int(bid_item[field])
                    break
            
            if not amount:
                return None
            
            # Extract timestamp
            timestamp = datetime.now()
            for field in time_fields:
                if field in bid_item and bid_item[field]:
                    try:
                        if isinstance(bid_item[field], (int, float)):
                            timestamp = datetime.fromtimestamp(bid_item[field] / 1000)
                        break
                    except:
                        continue
            
            return {
                'lot_id': lot_id,
                'amount': amount,
                'timestamp': timestamp,
                'bid_time': timestamp.strftime('%H:%M:%S'),
                'bid_date': timestamp.strftime('%Y-%m-%d')
            }
            
        except Exception as e:
            logger.debug(f"Error parsing bid JSON: {e}")
            return None
    
    def _remove_duplicate_bids(self, bids: List[Dict]) -> List[Dict]:
        """Remove duplicate bids"""
        seen = set()
        unique_bids = []
        
        for bid in bids:
            # Create unique key from amount and timestamp
            key = (bid.get('amount'), bid.get('timestamp'))
            if key not in seen:
                seen.add(key)
                unique_bids.append(bid)
        
        return unique_bids


if __name__ == "__main__":
    scraper = LotDetailsScraper()
    
    # Test with a specific lot ID
    test_lot_id = 123456  # This would come from lot discovery
    details = scraper.scrape_lot_details(test_lot_id)
    
    print(f"\n=== LOT DETAILS ===")
    print(f"Title: {details.get('title')}")
    print(f"Current Price: €{details.get('current_price', 0)}")
    print(f"Bids: {details.get('bid_count', 0)}")
