{% extends "base.html" %}

{% block title %}Timetable - Auction Scraper{% endblock %}

{% block content %}
<div class="col-12">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-calendar"></i> Lot Closing Timetable</h1>
        <div>
            <span class="badge bg-success me-2">Waiting</span>
            <span class="badge bg-warning me-2">Closing Soon</span>
            <span class="badge bg-danger me-2">Extended</span>
            <span class="badge bg-secondary">Closed</span>
        </div>
    </div>

    {% if timetable_data %}
        {% for date in sorted_dates %}
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="bi bi-calendar-day"></i> {{ date }} ({{ timetable_data[date]|length }} lots)</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>Lot</th>
                                <th>Title</th>
                                <th>Current Price</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in timetable_data[date] %}
                            {% set lot = item.lot %}
                            {% set status = item.status %}
                            <tr class="{% if status == 'Closing Soon' %}table-warning{% elif status == 'Extended' %}table-danger{% endif %}">
                                <td>
                                    {% if lot.closing_time %}
                                        <strong>{{ lot.closing_time | datetime_format('%H:%M') }}</strong>
                                    {% else %}
                                        <span class="text-muted">TBD</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('lot_details', lot_id=lot.lot_id) }}" class="text-decoration-none">
                                        <strong>{{ lot.lot_number }}</strong>
                                    </a>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 300px;">
                                        {{ lot.title }}
                                    </div>
                                    <small class="text-muted">
                                        Auction {{ lot.auction_id }} | {{ lot.bid_count }} bids
                                    </small>
                                </td>
                                <td>
                                    <strong>{{ lot.current_price | currency }}</strong>
                                    {% if lot.starting_price > 0 %}
                                        <br><small class="text-muted">Start: {{ lot.starting_price | currency }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge 
                                        {% if status == 'Waiting' %}bg-success
                                        {% elif status == 'Closing Soon' %}bg-warning
                                        {% elif status == 'Extended' %}bg-danger
                                        {% else %}bg-secondary{% endif %}">
                                        {{ status }}
                                    </span>
                                    <br>
                                    <span class="badge {{ lot.lifecycle_stage | status_badge }} status-badge">
                                        {{ lot.lifecycle_stage.replace('_', ' ').title() }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group-vertical btn-group-sm">
                                        <button class="btn btn-outline-primary btn-sm" onclick="triggerBidCollection({{ lot.lot_id }})">
                                            <i class="bi bi-currency-euro"></i> Collect Bids
                                        </button>
                                        <a href="{{ url_for('lot_details', lot_id=lot.lot_id) }}" class="btn btn-outline-info btn-sm">
                                            <i class="bi bi-eye"></i> View Details
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="bi bi-calendar-x fs-1 text-muted"></i>
                <h3 class="mt-3">No Upcoming Lots</h3>
                <p class="text-muted">No lots with closing times found in the next 7 days.</p>
                <a href="{{ url_for('dashboard') }}" class="btn btn-primary">
                    <i class="bi bi-house"></i> Back to Dashboard
                </a>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Timetable-specific JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        // Highlight rows based on closing time
        const now = new Date();
        const rows = document.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const timeCell = row.querySelector('td:first-child strong');
            if (timeCell) {
                const timeText = timeCell.textContent;
                const today = new Date().toISOString().split('T')[0];
                const closingTime = new Date(`${today}T${timeText}:00`);
                
                const timeDiff = closingTime - now;
                const minutesUntilClosing = timeDiff / (1000 * 60);
                
                if (minutesUntilClosing <= 5 && minutesUntilClosing > 0) {
                    row.classList.add('table-danger');
                    row.style.animation = 'pulse 2s infinite';
                } else if (minutesUntilClosing <= 60 && minutesUntilClosing > 5) {
                    row.classList.add('table-warning');
                }
            }
        });
        
        // Auto-refresh every 30 seconds for timetable
        setInterval(() => {
            location.reload();
        }, 30000);
    });
</script>
{% endblock %}
