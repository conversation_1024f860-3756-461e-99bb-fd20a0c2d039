#!/usr/bin/env python3
"""
Dynamic Auction Discovery System
Discovers ALL auctions from Aurena website without any hardcoded data
Uses only dynamic discovery methods from the actual website
"""

import requests
import sqlite3
from bs4 import BeautifulSoup
import re
import time
import json
from datetime import datetime, timedelta
from typing import List, Dict, Set, Optional
import logging
from urllib.parse import urljoin, urlparse

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DynamicAuctionDiscovery:
    """Dynamically discovers all auctions from Aurena website"""
    
    def __init__(self, db_path: str = "aurena_auctions.db"):
        self.db_path = db_path
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.base_url = "https://www.aurena.at"
        self.discovered_auction_ids: Set[int] = set()
        self.init_database()
    
    def init_database(self):
        """Initialize clean database"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Auctions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS auctions (
                    auction_id INTEGER PRIMARY KEY,
                    title TEXT NOT NULL,
                    start_time TEXT NOT NULL,
                    end_time TEXT,
                    lot_count INTEGER DEFAULT 0,
                    location TEXT,
                    status TEXT DEFAULT 'active',
                    lifecycle_stage TEXT DEFAULT 'discovered',
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            """)
            
            # Lots table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS lots (
                    lot_id INTEGER PRIMARY KEY,
                    auction_id INTEGER NOT NULL,
                    lot_number INTEGER,
                    title TEXT NOT NULL,
                    description TEXT,
                    starting_price INTEGER DEFAULT 0,
                    current_price INTEGER DEFAULT 0,
                    closing_time TEXT NOT NULL,
                    status TEXT DEFAULT 'active',
                    bid_count INTEGER DEFAULT 0,
                    images TEXT,
                    lifecycle_stage TEXT DEFAULT 'discovered',
                    bids_fetched BOOLEAN DEFAULT FALSE,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    FOREIGN KEY (auction_id) REFERENCES auctions (auction_id)
                )
            """)
            
            # Real bids table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS real_bids (
                    bid_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    lot_id INTEGER NOT NULL,
                    auction_id INTEGER NOT NULL,
                    amount INTEGER NOT NULL,
                    bid_date TEXT NOT NULL,
                    bid_time TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    fetched_at TEXT NOT NULL,
                    FOREIGN KEY (lot_id) REFERENCES lots (lot_id)
                )
            """)
            
            # Lifecycle log
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS lifecycle_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    auction_id INTEGER,
                    lot_id INTEGER,
                    action TEXT NOT NULL,
                    status TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    details TEXT
                )
            """)
            
            conn.commit()
            logger.info("Database initialized")
    
    def discover_all_auctions(self) -> List[Dict]:
        """
        Dynamically discover ALL auctions from Aurena website
        NO hardcoded data - pure dynamic discovery
        """
        logger.info("🔍 Starting DYNAMIC auction discovery (no hardcoded data)")
        
        all_auctions = []
        
        # Method 1: Parse main auction list pages
        auctions_from_main = self._discover_from_main_pages()
        all_auctions.extend(auctions_from_main)
        
        # Method 2: Parse sitemap if available
        auctions_from_sitemap = self._discover_from_sitemap()
        all_auctions.extend(auctions_from_sitemap)
        
        # Method 3: Parse RSS/feeds if available
        auctions_from_feeds = self._discover_from_feeds()
        all_auctions.extend(auctions_from_feeds)
        
        # Method 4: Parse JavaScript data
        auctions_from_js = self._discover_from_javascript()
        all_auctions.extend(auctions_from_js)
        
        # Method 5: Intelligent ID range scanning based on discovered patterns
        auctions_from_patterns = self._discover_from_patterns(all_auctions)
        all_auctions.extend(auctions_from_patterns)
        
        # Remove duplicates
        unique_auctions = self._remove_duplicates(all_auctions)
        
        # Save to database
        self._save_discovered_auctions(unique_auctions)
        
        logger.info(f"✅ Dynamic discovery complete: {len(unique_auctions)} unique auctions found")
        return unique_auctions
    
    def _discover_from_main_pages(self) -> List[Dict]:
        """Discover auctions from main website pages"""
        logger.info("📄 Discovering from main pages...")
        
        auctions = []
        
        # Try different potential auction list URLs
        potential_urls = [
            f"{self.base_url}/",
            f"{self.base_url}/auktionen",
            f"{self.base_url}/auctions", 
            f"{self.base_url}/current",
            f"{self.base_url}/aktuelle",
            f"{self.base_url}/laufende",
        ]
        
        for url in potential_urls:
            try:
                logger.info(f"Checking: {url}")
                response = self.session.get(url, timeout=30)
                
                if response.status_code == 200:
                    page_auctions = self._extract_auctions_from_html(response.text, url)
                    auctions.extend(page_auctions)
                    logger.info(f"Found {len(page_auctions)} auctions from {url}")
                
                time.sleep(2)  # Rate limiting
                
            except Exception as e:
                logger.debug(f"Error accessing {url}: {e}")
                continue
        
        logger.info(f"📄 Main pages: {len(auctions)} auctions discovered")
        return auctions
    
    def _discover_from_sitemap(self) -> List[Dict]:
        """Discover auctions from sitemap.xml"""
        logger.info("🗺️ Discovering from sitemap...")
        
        auctions = []
        sitemap_urls = [
            f"{self.base_url}/sitemap.xml",
            f"{self.base_url}/sitemap_index.xml",
            f"{self.base_url}/robots.txt"  # Check robots.txt for sitemap references
        ]
        
        for url in sitemap_urls:
            try:
                response = self.session.get(url, timeout=30)
                if response.status_code == 200:
                    # Extract auction URLs from sitemap
                    auction_urls = re.findall(r'<loc>([^<]*auktion[^<]*)</loc>', response.text, re.IGNORECASE)
                    
                    for auction_url in auction_urls:
                        auction_id = self._extract_auction_id_from_url(auction_url)
                        if auction_id and auction_id not in self.discovered_auction_ids:
                            auction_data = self._fetch_auction_details(auction_id)
                            if auction_data:
                                auctions.append(auction_data)
                                self.discovered_auction_ids.add(auction_id)
                
                time.sleep(1)
                
            except Exception as e:
                logger.debug(f"Error accessing sitemap {url}: {e}")
                continue
        
        logger.info(f"🗺️ Sitemap: {len(auctions)} auctions discovered")
        return auctions
    
    def _discover_from_feeds(self) -> List[Dict]:
        """Discover auctions from RSS feeds or API endpoints"""
        logger.info("📡 Discovering from feeds/APIs...")
        
        auctions = []
        feed_urls = [
            f"{self.base_url}/rss",
            f"{self.base_url}/feed",
            f"{self.base_url}/api/auctions",
            f"{self.base_url}/api/current",
        ]
        
        for url in feed_urls:
            try:
                response = self.session.get(url, timeout=30)
                if response.status_code == 200:
                    # Try to parse as JSON first
                    try:
                        data = response.json()
                        if isinstance(data, list):
                            for item in data:
                                auction_data = self._parse_api_auction_data(item)
                                if auction_data:
                                    auctions.append(auction_data)
                        elif isinstance(data, dict) and 'auctions' in data:
                            for item in data['auctions']:
                                auction_data = self._parse_api_auction_data(item)
                                if auction_data:
                                    auctions.append(auction_data)
                    except:
                        # Try to parse as XML/RSS
                        auction_urls = re.findall(r'<link>([^<]*auktion[^<]*)</link>', response.text, re.IGNORECASE)
                        for auction_url in auction_urls:
                            auction_id = self._extract_auction_id_from_url(auction_url)
                            if auction_id and auction_id not in self.discovered_auction_ids:
                                auction_data = self._fetch_auction_details(auction_id)
                                if auction_data:
                                    auctions.append(auction_data)
                                    self.discovered_auction_ids.add(auction_id)
                
                time.sleep(1)
                
            except Exception as e:
                logger.debug(f"Error accessing feed {url}: {e}")
                continue
        
        logger.info(f"📡 Feeds: {len(auctions)} auctions discovered")
        return auctions
    
    def _discover_from_javascript(self) -> List[Dict]:
        """Discover auctions from JavaScript data on main pages"""
        logger.info("🔧 Discovering from JavaScript data...")
        
        auctions = []
        
        try:
            response = self.session.get(self.base_url, timeout=30)
            if response.status_code == 200:
                # Look for JavaScript objects containing auction data
                js_patterns = [
                    r'auctions\s*[:=]\s*(\[.*?\])',
                    r'auctionList\s*[:=]\s*(\[.*?\])',
                    r'data\s*[:=]\s*(\{.*?"auctions".*?\})',
                    r'window\.auctions\s*=\s*(\[.*?\])',
                ]
                
                for pattern in js_patterns:
                    matches = re.findall(pattern, response.text, re.DOTALL | re.IGNORECASE)
                    for match in matches:
                        try:
                            # Try to parse as JSON
                            data = json.loads(match)
                            if isinstance(data, list):
                                for item in data:
                                    auction_data = self._parse_api_auction_data(item)
                                    if auction_data:
                                        auctions.append(auction_data)
                        except:
                            continue
                
        except Exception as e:
            logger.debug(f"Error parsing JavaScript data: {e}")
        
        logger.info(f"🔧 JavaScript: {len(auctions)} auctions discovered")
        return auctions
    
    def _discover_from_patterns(self, existing_auctions: List[Dict]) -> List[Dict]:
        """Intelligently discover auctions using smart ID range scanning"""
        logger.info("🧠 Discovering from intelligent ID scanning...")

        auctions = []

        # If no existing auctions, start with intelligent range scanning
        if not existing_auctions:
            # Start with recent ID ranges (auctions are typically sequential)
            # Scan recent ranges to find active auctions
            current_year = datetime.now().year
            estimated_start_id = (current_year - 2020) * 1000 + 14000  # Rough estimate

            scan_ranges = [
                (estimated_start_id, estimated_start_id + 200),  # Recent range
                (14000, 14200),  # Known active range
                (15000, 15200),  # Another potential range
            ]
        else:
            # Use existing auctions to determine patterns
            auction_ids = [a['auction_id'] for a in existing_auctions]
            min_id = min(auction_ids)
            max_id = max(auction_ids)

            scan_ranges = [
                (min_id - 100, min_id),  # Before minimum
                (max_id, max_id + 100),  # After maximum
            ]

            # Fill gaps in discovered ranges
            for i in range(len(auction_ids) - 1):
                current_id = auction_ids[i]
                next_id = auction_ids[i + 1]
                if next_id - current_id > 10:  # Gap larger than 10
                    scan_ranges.append((current_id + 1, min(current_id + 50, next_id)))

        # Scan the ranges
        for start_id, end_id in scan_ranges:
            logger.info(f"Scanning ID range {start_id}-{end_id}...")
            found_in_range = 0

            for auction_id in range(start_id, end_id + 1):
                if auction_id not in self.discovered_auction_ids:
                    auction_data = self._fetch_auction_details(auction_id)
                    if auction_data:
                        auctions.append(auction_data)
                        self.discovered_auction_ids.add(auction_id)
                        found_in_range += 1
                        logger.info(f"Found auction {auction_id}: {auction_data['title'][:40]}...")

                    time.sleep(0.3)  # Rate limiting

                    # Stop scanning this range if we find too many gaps
                    if auction_id - start_id > 50 and found_in_range == 0:
                        logger.info(f"No auctions found in range {start_id}-{auction_id}, stopping range scan")
                        break

            logger.info(f"Found {found_in_range} auctions in range {start_id}-{end_id}")

        logger.info(f"🧠 Intelligent scanning: {len(auctions)} auctions discovered")
        return auctions

    def _extract_auctions_from_html(self, html_content: str, source_url: str) -> List[Dict]:
        """Extract auction IDs and data from HTML content"""
        auctions = []

        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # Look for auction links
            auction_links = soup.find_all('a', href=re.compile(r'/auktion/\d+'))

            for link in auction_links:
                auction_id = self._extract_auction_id_from_url(link['href'])
                if auction_id and auction_id not in self.discovered_auction_ids:
                    auction_data = self._fetch_auction_details(auction_id)
                    if auction_data:
                        auctions.append(auction_data)
                        self.discovered_auction_ids.add(auction_id)

                    time.sleep(0.3)  # Rate limiting

            # Also look for auction IDs in text content
            auction_id_matches = re.findall(r'auktion[/\s]*(\d+)', html_content, re.IGNORECASE)
            for auction_id_str in set(auction_id_matches):
                try:
                    auction_id = int(auction_id_str)
                    if auction_id not in self.discovered_auction_ids:
                        auction_data = self._fetch_auction_details(auction_id)
                        if auction_data:
                            auctions.append(auction_data)
                            self.discovered_auction_ids.add(auction_id)

                        time.sleep(0.3)
                except ValueError:
                    continue

        except Exception as e:
            logger.debug(f"Error extracting auctions from HTML: {e}")

        return auctions

    def _extract_auction_id_from_url(self, url: str) -> Optional[int]:
        """Extract auction ID from URL"""
        try:
            match = re.search(r'/auktion/(\d+)', url)
            if match:
                return int(match.group(1))
        except:
            pass
        return None

    def _parse_api_auction_data(self, data: Dict) -> Optional[Dict]:
        """Parse auction data from API response"""
        try:
            auction_id = data.get('id') or data.get('auction_id')
            if not auction_id:
                return None

            auction_id = int(auction_id)
            if auction_id in self.discovered_auction_ids:
                return None

            title = data.get('title', f'Auction {auction_id}')
            start_time = data.get('start_date') or data.get('starting_date') or datetime.now().isoformat()
            end_time = data.get('end_date') or data.get('ending_date') or (datetime.now() + timedelta(days=7)).isoformat()
            lot_count = data.get('lot_count', 0)
            location = data.get('location') or data.get('location_city', 'Austria')
            status = data.get('status', 'active')

            self.discovered_auction_ids.add(auction_id)

            return {
                'auction_id': auction_id,
                'title': title,
                'start_time': start_time,
                'end_time': end_time,
                'lot_count': lot_count,
                'location': location,
                'status': status,
                'lifecycle_stage': 'discovered'
            }

        except Exception as e:
            logger.debug(f"Error parsing API auction data: {e}")
            return None

    def _fetch_auction_details(self, auction_id: int) -> Optional[Dict]:
        """Fetch detailed auction information"""
        try:
            url = f"{self.base_url}/auktion/{auction_id}"
            response = self.session.get(url, timeout=15)

            if response.status_code != 200:
                return None

            soup = BeautifulSoup(response.content, 'html.parser')

            # Check if it's a valid auction page
            if "nicht gefunden" in response.text.lower() or "not found" in response.text.lower():
                return None

            # Extract title
            title_elem = soup.find('h1') or soup.find('title')
            title = title_elem.get_text(strip=True) if title_elem else f"Auction {auction_id}"

            # Skip error pages
            if "error" in title.lower() or "nicht gefunden" in title.lower():
                return None

            # Count lots
            lot_count = len(re.findall(r'/posten/\d+', response.text))

            # Extract location
            location = self._extract_location_from_page(response.text)

            # Determine status
            status = self._determine_auction_status(response.text)

            # Extract dates (basic approach)
            current_time = datetime.now()
            start_time = current_time.isoformat()
            end_time = (current_time + timedelta(days=7)).isoformat()

            return {
                'auction_id': auction_id,
                'title': title,
                'start_time': start_time,
                'end_time': end_time,
                'lot_count': lot_count,
                'location': location,
                'status': status,
                'lifecycle_stage': 'discovered'
            }

        except Exception as e:
            logger.debug(f"Error fetching auction {auction_id}: {e}")
            return None

    def _extract_location_from_page(self, page_content: str) -> str:
        """Extract location from page content"""
        # Look for Austrian cities and locations
        location_patterns = [
            r'(\b(?:Wien|Vienna|Graz|Linz|Salzburg|Innsbruck|Klagenfurt|Bregenz|St\.\s*Pölten|Eisenstadt)\b)',
            r'(\b\d{4}\s+[A-ZÄÖÜ][a-zäöüß]+\b)',  # Postal code + city
        ]

        for pattern in location_patterns:
            matches = re.findall(pattern, page_content, re.IGNORECASE)
            if matches:
                return matches[0] if isinstance(matches[0], str) else matches[0][0]

        return 'Austria'  # Default

    def _determine_auction_status(self, page_content: str) -> str:
        """Determine auction status from page content"""
        content_lower = page_content.lower()

        if any(word in content_lower for word in ['beendet', 'closed', 'abgeschlossen']):
            return 'closed'
        elif any(word in content_lower for word in ['läuft', 'active', 'laufend', 'aktiv']):
            return 'active'
        else:
            return 'active'  # Default

    def _remove_duplicates(self, auctions: List[Dict]) -> List[Dict]:
        """Remove duplicate auctions"""
        seen_ids = set()
        unique_auctions = []

        for auction in auctions:
            auction_id = auction['auction_id']
            if auction_id not in seen_ids:
                seen_ids.add(auction_id)
                unique_auctions.append(auction)

        return unique_auctions

    def _save_discovered_auctions(self, auctions: List[Dict]):
        """Save discovered auctions to database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                current_time = datetime.now().isoformat()

                for auction in auctions:
                    cursor.execute("""
                        INSERT OR REPLACE INTO auctions
                        (auction_id, title, start_time, end_time, lot_count,
                         location, status, lifecycle_stage, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        auction['auction_id'], auction['title'], auction['start_time'],
                        auction['end_time'], auction['lot_count'], auction['location'],
                        auction['status'], auction['lifecycle_stage'], current_time, current_time
                    ))

                conn.commit()
                logger.info(f"💾 Saved {len(auctions)} auctions to database")

        except Exception as e:
            logger.error(f"Error saving auctions: {e}")

def main():
    """Run dynamic auction discovery"""
    print("🔍 DYNAMIC AUCTION DISCOVERY")
    print("=" * 60)
    print("NO HARDCODED DATA - Pure dynamic discovery from Aurena website")
    print("Discovering ALL auctions using multiple dynamic methods...")
    print("=" * 60)

    discovery = DynamicAuctionDiscovery()
    auctions = discovery.discover_all_auctions()

    print(f"\n🎉 DYNAMIC DISCOVERY COMPLETE!")
    print(f"✅ Found {len(auctions)} unique auctions (NO hardcoded data)")

    if auctions:
        print(f"\n📋 SAMPLE DISCOVERED AUCTIONS:")
        for i, auction in enumerate(auctions[:10]):
            print(f"   {i+1}. Auction {auction['auction_id']}: {auction['title'][:50]}...")
            print(f"      Lots: {auction['lot_count']}, Status: {auction['status']}")

    print(f"\n💾 All auctions saved to database")
    print(f"🌐 View them at: http://localhost:5000")

if __name__ == "__main__":
    main()
