#!/usr/bin/env python3
"""
Main Entry Point for Auction Scraper System
Provides command-line interface for running the scraper and dashboard.
"""

import argparse
import sys
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.database.manager import DatabaseManager
from src.scheduler.auction_scheduler import AuctionScheduler
from src.web.dashboard import AuctionDashboard
from src.scraper.auction_list_scraper import AuctionListScraper
from src.scraper.lot_list_scraper import LotListScraper
from src.scraper.lot_details_scraper import LotDetailsScraper

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_dashboard(args):
    """Run the web dashboard"""
    logger.info("🌐 Starting Auction Scraper Dashboard...")
    
    try:
        dashboard = AuctionDashboard(db_path=args.database)
        dashboard.run(host=args.host, port=args.port, debug=args.debug)
    except KeyboardInterrupt:
        logger.info("Dashboard stopped by user")
    except Exception as e:
        logger.error(f"Error running dashboard: {e}")
        sys.exit(1)


def run_scraper_only(args):
    """Run scraper operations without dashboard"""
    logger.info("🕷️ Starting Auction Scraper (CLI mode)...")
    
    try:
        db_manager = DatabaseManager(args.database)
        scheduler = AuctionScheduler(db_manager)
        
        if args.discover_auctions:
            logger.info("Discovering auctions...")
            scheduler.trigger_manual_auction_discovery()
        
        if args.discover_lots:
            if args.auction_id:
                logger.info(f"Discovering lots for auction {args.auction_id}...")
                scheduler.trigger_manual_lot_discovery(args.auction_id)
            else:
                logger.error("--auction-id required for lot discovery")
                sys.exit(1)
        
        if args.collect_bids:
            if args.lot_id:
                logger.info(f"Collecting bids for lot {args.lot_id}...")
                scheduler.trigger_manual_bid_collection(args.lot_id)
            else:
                logger.error("--lot-id required for bid collection")
                sys.exit(1)
        
        if args.daemon:
            logger.info("Starting scheduler daemon...")
            scheduler.start()
            try:
                while True:
                    import time
                    time.sleep(1)
            except KeyboardInterrupt:
                logger.info("Scheduler stopped by user")
                scheduler.stop()
        
    except Exception as e:
        logger.error(f"Error running scraper: {e}")
        sys.exit(1)


def test_scrapers(args):
    """Test scraper functionality"""
    logger.info("🧪 Testing Scraper Components...")
    
    try:
        # Test auction list scraper
        logger.info("Testing auction list scraper...")
        auction_scraper = AuctionListScraper()
        auctions = auction_scraper.discover_auctions()
        logger.info(f"✅ Discovered {len(auctions)} auctions")
        
        if auctions and args.full_test:
            # Test lot list scraper with first auction
            test_auction = auctions[0]
            logger.info(f"Testing lot list scraper with auction {test_auction['auction_id']}...")
            lot_scraper = LotListScraper()
            lots = lot_scraper.scrape_auction_lots(test_auction['auction_id'])
            logger.info(f"✅ Extracted {len(lots)} lots")
            
            if lots:
                # Test lot details scraper with first lot
                test_lot = lots[0]
                logger.info(f"Testing lot details scraper with lot {test_lot['lot_id']}...")
                details_scraper = LotDetailsScraper()
                details = details_scraper.scrape_lot_details(test_lot['lot_id'])
                logger.info(f"✅ Extracted lot details: {details.get('title', 'Unknown')}")
        
        logger.info("🎉 All scraper tests completed successfully!")
        
    except Exception as e:
        logger.error(f"Error testing scrapers: {e}")
        sys.exit(1)


def show_stats(args):
    """Show database statistics"""
    try:
        db_manager = DatabaseManager(args.database)
        stats = db_manager.get_statistics()
        
        print("\n" + "="*50)
        print("📊 AUCTION SCRAPER STATISTICS")
        print("="*50)
        print(f"Total Auctions: {stats.get('total_auctions', 0)}")
        print(f"Total Lots: {stats.get('total_lots', 0)}")
        print(f"Total Bids: {stats.get('total_bids', 0)}")
        print(f"Active Lots: {stats.get('active_lots', 0)}")
        print(f"Lots with Bids: {stats.get('lots_with_bids', 0)}")
        print("="*50)
        
        # Show recent auctions
        recent_auctions = db_manager.get_auctions(limit=5)
        if recent_auctions:
            print("\n📋 Recent Auctions:")
            for auction in recent_auctions:
                print(f"  • {auction.auction_id}: {auction.title} ({auction.lot_count} lots)")
        
        # Show upcoming lots
        upcoming_lots = db_manager.get_upcoming_lots(hours=24)
        if upcoming_lots:
            print(f"\n⏰ Upcoming Lots (24h): {len(upcoming_lots)}")
            for lot in upcoming_lots[:5]:
                closing_time = lot.closing_time.strftime('%m/%d %H:%M') if lot.closing_time else 'TBD'
                print(f"  • Lot {lot.lot_number}: {lot.title[:50]}... (closes {closing_time})")
        
        print()
        
    except Exception as e:
        logger.error(f"Error showing stats: {e}")
        sys.exit(1)


def main():
    """Main function with command-line interface"""
    parser = argparse.ArgumentParser(
        description='Auction Scraper System - Dynamic auction data collection and monitoring',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s dashboard                    # Start web dashboard
  %(prog)s scraper --discover-auctions  # Discover auctions only
  %(prog)s scraper --daemon             # Run scheduler daemon
  %(prog)s test                         # Test scraper components
  %(prog)s stats                        # Show database statistics
        """
    )
    
    # Global options
    parser.add_argument('--database', '-d', default='data/auction_scraper.db',
                       help='Database file path (default: data/auction_scraper.db)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Dashboard command
    dashboard_parser = subparsers.add_parser('dashboard', help='Start web dashboard')
    dashboard_parser.add_argument('--host', default='0.0.0.0',
                                 help='Host to bind to (default: 0.0.0.0)')
    dashboard_parser.add_argument('--port', '-p', type=int, default=5000,
                                 help='Port to bind to (default: 5000)')
    dashboard_parser.add_argument('--debug', action='store_true',
                                 help='Enable debug mode')
    
    # Scraper command
    scraper_parser = subparsers.add_parser('scraper', help='Run scraper operations')
    scraper_parser.add_argument('--discover-auctions', action='store_true',
                               help='Discover all auctions')
    scraper_parser.add_argument('--discover-lots', action='store_true',
                               help='Discover lots for an auction')
    scraper_parser.add_argument('--collect-bids', action='store_true',
                               help='Collect bids for a lot')
    scraper_parser.add_argument('--auction-id', type=int,
                               help='Auction ID for lot discovery')
    scraper_parser.add_argument('--lot-id', type=int,
                               help='Lot ID for bid collection')
    scraper_parser.add_argument('--daemon', action='store_true',
                               help='Run as daemon with scheduler')
    
    # Test command
    test_parser = subparsers.add_parser('test', help='Test scraper components')
    test_parser.add_argument('--full-test', action='store_true',
                            help='Run full test including lot and bid scraping')
    
    # Stats command
    stats_parser = subparsers.add_parser('stats', help='Show database statistics')
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Ensure data directory exists
    Path(args.database).parent.mkdir(parents=True, exist_ok=True)
    
    # Print banner
    print("=" * 60)
    print("🎯 AURENA AUCTION SCRAPER SYSTEM")
    print("=" * 60)
    print("Dynamic auction data collection with NO hardcoded data")
    print("Three main functionalities:")
    print("  1. Auction list scraping")
    print("  2. Lot list scraping for auctions") 
    print("  3. Lot details with offer list scraping")
    print("=" * 60)
    
    # Route to appropriate function
    if args.command == 'dashboard':
        run_dashboard(args)
    elif args.command == 'scraper':
        run_scraper_only(args)
    elif args.command == 'test':
        test_scrapers(args)
    elif args.command == 'stats':
        show_stats(args)
    else:
        # Default to dashboard if no command specified
        print("No command specified, starting dashboard...")
        print(f"🌐 Access the dashboard at: http://localhost:5000")
        print("Use --help for more options")
        print()
        
        # Create default args for dashboard
        args.host = '0.0.0.0'
        args.port = 5000
        args.debug = False
        run_dashboard(args)


if __name__ == "__main__":
    main()
