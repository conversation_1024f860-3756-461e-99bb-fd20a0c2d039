{% extends "base.html" %}

{% block title %}Auction {{ auction.auction_id }} - Auction Scraper{% endblock %}

{% block content %}
<div class="col-12">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('auctions_list') }}">Auctions</a></li>
            <li class="breadcrumb-item active">Auction {{ auction.auction_id }}</li>
        </ol>
    </nav>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-hammer"></i> {{ auction.title }}</h1>
        <div class="btn-group">
            <button class="btn btn-primary" onclick="triggerLotDiscovery({{ auction.auction_id }})">
                <i class="bi bi-search"></i> Discover Lots
            </button>
            <a href="{{ url_for('auctions_list') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Auctions
            </a>
        </div>
    </div>

    <!-- Auction Information -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-info-circle"></i> Auction Information</h5>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-3">Auction ID:</dt>
                        <dd class="col-sm-9">{{ auction.auction_id }}</dd>
                        
                        <dt class="col-sm-3">Title:</dt>
                        <dd class="col-sm-9">{{ auction.title }}</dd>
                        
                        <dt class="col-sm-3">Start Time:</dt>
                        <dd class="col-sm-9">
                            {% if auction.start_time %}
                                {{ auction.start_time | datetime_format('%Y-%m-%d %H:%M:%S') }}
                            {% else %}
                                <span class="text-muted">Not specified</span>
                            {% endif %}
                        </dd>
                        
                        <dt class="col-sm-3">End Time:</dt>
                        <dd class="col-sm-9">
                            {% if auction.end_time %}
                                {{ auction.end_time | datetime_format('%Y-%m-%d %H:%M:%S') }}
                            {% else %}
                                <span class="text-muted">Not specified</span>
                            {% endif %}
                        </dd>
                        
                        <dt class="col-sm-3">Location:</dt>
                        <dd class="col-sm-9">
                            {% if auction.location %}
                                <i class="bi bi-geo-alt"></i> {{ auction.location }}
                            {% else %}
                                <span class="text-muted">Not specified</span>
                            {% endif %}
                        </dd>
                        
                        <dt class="col-sm-3">Total Lots:</dt>
                        <dd class="col-sm-9">
                            <span class="badge bg-secondary">{{ auction.lot_count }}</span>
                        </dd>
                        
                        <dt class="col-sm-3">Status:</dt>
                        <dd class="col-sm-9">
                            <span class="badge {{ auction.status | status_badge }}">{{ auction.status }}</span>
                        </dd>
                        
                        <dt class="col-sm-3">Lifecycle Stage:</dt>
                        <dd class="col-sm-9">
                            <span class="badge {{ auction.lifecycle_stage | status_badge }} status-badge">
                                {{ auction.lifecycle_stage.replace('_', ' ').title() }}
                            </span>
                        </dd>
                    </dl>
                    
                    {% if auction.description %}
                    <hr>
                    <h6>Description:</h6>
                    <p>{{ auction.description }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-clock"></i> Timeline</h5>
                </div>
                <div class="card-body">
                    <small class="text-muted">Created:</small><br>
                    {{ auction.created_at | datetime_format('%Y-%m-%d %H:%M') }}<br><br>
                    
                    <small class="text-muted">Last Updated:</small><br>
                    {{ auction.updated_at | datetime_format('%Y-%m-%d %H:%M') }}<br>
                    <small class="text-muted">({{ auction.updated_at | time_ago }})</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Lots Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5><i class="bi bi-box"></i> Lots ({{ lots|length }})</h5>
            {% if lots %}
                <button class="btn btn-sm btn-success" onclick="triggerAllBidsForAuction({{ auction.auction_id }})">
                    <i class="bi bi-currency-euro"></i> Collect All Bids
                </button>
            {% endif %}
        </div>
        <div class="card-body">
            {% if lots %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Lot #</th>
                                <th>Title</th>
                                <th>Current Price</th>
                                <th>Closing Time</th>
                                <th>Bids</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for lot in lots %}
                            <tr>
                                <td>
                                    <strong>{{ lot.lot_number }}</strong>
                                </td>
                                <td>
                                    <a href="{{ url_for('lot_details', lot_id=lot.lot_id) }}" class="text-decoration-none">
                                        {{ lot.title[:50] }}{% if lot.title|length > 50 %}...{% endif %}
                                    </a>
                                </td>
                                <td>
                                    <strong>{{ lot.current_price | currency }}</strong>
                                    {% if lot.starting_price > 0 %}
                                        <br><small class="text-muted">Start: {{ lot.starting_price | currency }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if lot.closing_time %}
                                        {{ lot.closing_time | datetime_format('%m/%d %H:%M') }}
                                        {% set now = moment() %}
                                        {% if lot.closing_time < now %}
                                            <br><small class="text-danger">Closed</small>
                                        {% elif lot.closing_time < now + timedelta(hours=1) %}
                                            <br><small class="text-warning">Closing Soon</small>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">TBD</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ lot.bid_count }}</span>
                                    {% if lot.bids_fetched %}
                                        <br><small class="text-success"><i class="bi bi-check"></i> Fetched</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge {{ lot.status | status_badge }}">{{ lot.status }}</span>
                                    <br>
                                    <span class="badge {{ lot.lifecycle_stage | status_badge }} status-badge">
                                        {{ lot.lifecycle_stage.replace('_', ' ').title() }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group-vertical btn-group-sm">
                                        <a href="{{ url_for('lot_details', lot_id=lot.lot_id) }}" class="btn btn-outline-info btn-sm">
                                            <i class="bi bi-eye"></i> View
                                        </a>
                                        <button class="btn btn-outline-primary btn-sm" onclick="triggerBidCollection({{ lot.lot_id }})">
                                            <i class="bi bi-currency-euro"></i> Bids
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center text-muted py-5">
                    <i class="bi bi-box fs-1"></i>
                    <h4 class="mt-3">No Lots Found</h4>
                    <p>No lots have been discovered for this auction yet.</p>
                    <button class="btn btn-primary" onclick="triggerLotDiscovery({{ auction.auction_id }})">
                        <i class="bi bi-search"></i> Discover Lots
                    </button>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    async function triggerAllBidsForAuction(auctionId) {
        const button = event.target;
        const originalText = button.innerHTML;
        
        button.disabled = true;
        button.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Collecting...';
        
        try {
            showAlert('info', 'Triggering bid collection for all lots in this auction...');
            
            // Get all lot IDs from the table
            const lotRows = document.querySelectorAll('tbody tr');
            let successCount = 0;
            let totalLots = lotRows.length;
            
            for (let row of lotRows) {
                const lotLink = row.querySelector('a[href*="/lot/"]');
                if (lotLink) {
                    const lotId = lotLink.href.split('/lot/')[1];
                    try {
                        const result = await apiCall(`/api/collect-bids/${lotId}`, 'POST');
                        if (result.success) {
                            successCount++;
                        }
                    } catch (error) {
                        console.error(`Error collecting bids for lot ${lotId}:`, error);
                    }
                }
                
                // Small delay between requests
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            showAlert('success', `Triggered bid collection for ${successCount}/${totalLots} lots`);
            setTimeout(() => location.reload(), 3000);
            
        } catch (error) {
            showAlert('danger', 'Error: ' + error.message);
        } finally {
            button.disabled = false;
            button.innerHTML = originalText;
        }
    }
</script>
{% endblock %}
