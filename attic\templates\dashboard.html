<!DOCTYPE html>
<html>
<head>
    <title>Aurena Auction Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .auction-tree {
            max-height: 400px;
            overflow-y: auto;
        }
        .lot-item {
            cursor: pointer;
            padding: 5px;
            border-radius: 3px;
        }
        .lot-item:hover {
            background-color: #f8f9fa;
        }
        .status-waiting { color: #6c757d; }
        .status-active { color: #198754; }
        .status-closing { color: #fd7e14; }
        .status-critical { color: #dc3545; }
        .status-closed { color: #6c757d; }
        .timetable-row {
            border-left: 4px solid #dee2e6;
        }
        .timetable-row.status-waiting { border-left-color: #6c757d; }
        .timetable-row.status-active { border-left-color: #198754; }
        .timetable-row.status-closing { border-left-color: #fd7e14; }
        .timetable-row.status-critical { border-left-color: #dc3545; }
        .timetable-row.status-closed { border-left-color: #6c757d; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-gavel me-2"></i>
                Aurena Auction Tracker
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Dashboard</a>
                <a class="nav-link" href="/timetable">Timetable</a>
                <a class="nav-link" href="/auction-tree">Auction Tree</a>
                <a class="nav-link" href="/scraping-control">🔧 Scraping Control</a>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h4>{{ stats.total_auctions or 0 }}</h4>
                        <p>Total Auctions</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <h4>{{ stats.total_lots or 0 }}</h4>
                        <p>Total Lots</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <h4>{{ stats.lots_with_bids or 0 }}</h4>
                        <p>Lots with Bids</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <h4>{{ stats.upcoming_lots or 0 }}</h4>
                        <p>Closing Soon</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Auction Tree Navigation -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-sitemap me-2"></i>Auction Tree Navigation</h5>
                    </div>
                    <div class="card-body auction-tree">
                        {% if auction_tree %}
                            {% for auction in auction_tree %}
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-1">
                                        <a href="/auction/{{ auction.auction_id }}" class="text-decoration-none">
                                            <i class="fas fa-gavel me-1"></i>
                                            Auction {{ auction.auction_id }}
                                        </a>
                                    </h6>
                                    <span class="badge bg-{{ 'success' if auction.timetable_status == 'Active' else 'secondary' }}">
                                        {{ auction.timetable_status }}
                                    </span>
                                </div>
                                <p class="text-muted small mb-2">{{ auction.title[:50] }}{% if auction.title|length > 50 %}...{% endif %}</p>
                                <div class="d-flex justify-content-between text-small">
                                    <span><i class="fas fa-box me-1"></i>{{ auction.lot_count }} lots</span>
                                    <span><i class="fas fa-euro-sign me-1"></i>{{ auction.lots_with_bids }} with bids</span>
                                </div>
                                <div class="text-muted small">
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ auction.start_time.strftime('%Y-%m-%d %H:%M') }} - {{ auction.end_time.strftime('%Y-%m-%d %H:%M') }}
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted">No auctions found.</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Timetable - Closing Soon -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-clock me-2"></i>Timetable - Closing Soon (48h)</h5>
                        <a href="/timetable" class="btn btn-sm btn-outline-primary">View Full Timetable</a>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        {% if upcoming_lots %}
                            {% for lot in upcoming_lots %}
                            <div class="timetable-row p-3 mb-2 border rounded">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <a href="/lot/{{ lot.lot_id }}" class="text-decoration-none">
                                                Lot {{ lot.lot_number }}: {{ lot.title[:40] }}{% if lot.title|length > 40 %}...{% endif %}
                                            </a>
                                        </h6>
                                        <p class="text-muted small mb-1">
                                            <i class="fas fa-gavel me-1"></i>
                                            <a href="/auction/{{ lot.auction_id }}" class="text-decoration-none">
                                                Auction {{ lot.auction_id }}
                                            </a>
                                        </p>
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-{{ lot.status_class or 'secondary' }} me-2">
                                                {{ lot.timetable_status or 'Unknown' }}
                                            </span>
                                            <span class="text-success fw-bold">€{{ lot.current_price }}</span>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <div class="text-muted small">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ lot.closing_time.strftime('%H:%M:%S') }}
                                        </div>
                                        <div class="text-muted small">
                                            {{ lot.closing_time.strftime('%Y-%m-%d') }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted">No lots closing soon.</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-refresh every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);

        // Update countdown timers
        function updateCountdowns() {
            const now = new Date();
            document.querySelectorAll('[data-closing-time]').forEach(function(element) {
                const closingTime = new Date(element.dataset.closingTime);
                const diff = closingTime - now;

                if (diff > 0) {
                    const hours = Math.floor(diff / (1000 * 60 * 60));
                    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

                    if (hours > 0) {
                        element.textContent = `${hours}h ${minutes}m ${seconds}s`;
                    } else if (minutes > 0) {
                        element.textContent = `${minutes}m ${seconds}s`;
                    } else {
                        element.textContent = `${seconds}s`;
                    }
                } else {
                    element.textContent = 'CLOSED';
                    element.className = 'badge bg-secondary';
                }
            });
        }

        // Update countdowns every second
        setInterval(updateCountdowns, 1000);
        updateCountdowns(); // Initial call
    </script>
</body>
</html>
