{% extends "base.html" %}

{% block title %}Scraping Control - Auction Scraper{% endblock %}

{% block content %}
<div class="col-12">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-gear"></i> Scraping Control Panel</h1>
        <div class="btn-group">
            <button class="btn btn-success" onclick="triggerAuctionDiscovery()">
                <i class="bi bi-search"></i> Discover Auctions
            </button>
            <button class="btn btn-info" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise"></i> Refresh
            </button>
        </div>
    </div>

    <!-- Control Actions -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-play-circle"></i> Manual Triggers</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" onclick="triggerAuctionDiscovery()">
                            <i class="bi bi-search"></i> Discover All Auctions
                        </button>
                        <button class="btn btn-warning" onclick="triggerAllLotDiscovery()">
                            <i class="bi bi-box"></i> Discover All Lots
                        </button>
                        <button class="btn btn-success" onclick="triggerAllBidCollection()">
                            <i class="bi bi-currency-euro"></i> Collect All Bids
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-graph-up"></i> System Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <h4 class="text-primary">{{ stats.total_auctions or 0 }}</h4>
                            <small class="text-muted">Auctions</small>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-success">{{ stats.total_lots or 0 }}</h4>
                            <small class="text-muted">Lots</small>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-warning">{{ stats.total_bids or 0 }}</h4>
                            <small class="text-muted">Bids</small>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-info">{{ stats.active_lots or 0 }}</h4>
                            <small class="text-muted">Active</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Scheduled Tasks -->
        <div class="col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="bi bi-calendar-check"></i> Scheduled Tasks</h5>
                </div>
                <div class="card-body">
                    {% if scheduled_tasks %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Type</th>
                                        <th>Target</th>
                                        <th>Scheduled</th>
                                        <th>Status</th>
                                        <th>Priority</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for task in scheduled_tasks[:20] %}
                                    <tr>
                                        <td>
                                            <small class="badge bg-secondary">
                                                {{ task.task_type.replace('_', ' ').title() }}
                                            </small>
                                        </td>
                                        <td>
                                            {% if task.target_id %}
                                                <small>{{ task.target_id }}</small>
                                            {% else %}
                                                <small class="text-muted">All</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <small>{{ task.scheduled_time | datetime_format('%m/%d %H:%M') }}</small>
                                        </td>
                                        <td>
                                            <span class="badge {{ task.status | status_badge }} status-badge">
                                                {{ task.status }}
                                            </span>
                                        </td>
                                        <td>
                                            <small>{{ task.priority }}</small>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="bi bi-calendar-x fs-1"></i>
                            <p>No scheduled tasks</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Activity Log -->
        <div class="col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="bi bi-activity"></i> Activity Log</h5>
                </div>
                <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                    {% if recent_logs %}
                        <div class="list-group list-group-flush">
                            {% for log in recent_logs %}
                            <div class="list-group-item px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold">{{ log.action.replace('_', ' ').title() }}</div>
                                        <small class="text-muted">
                                            {% if log.auction_id %}Auction {{ log.auction_id }}{% endif %}
                                            {% if log.lot_id %}Lot {{ log.lot_id }}{% endif %}
                                            {% if log.details %} - {{ log.details }}{% endif %}
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge {{ log.status | status_badge }} status-badge">{{ log.status }}</span>
                                        <br>
                                        <small class="text-muted">{{ log.timestamp | time_ago }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="bi bi-journal-x fs-1"></i>
                            <p>No activity logs</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Control panel specific functions
    async function triggerAllLotDiscovery() {
        const button = event.target;
        const originalText = button.innerHTML;
        
        button.disabled = true;
        button.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Discovering...';
        
        try {
            // Get all auctions first
            const statsResponse = await apiCall('/api/stats');
            if (statsResponse.total_auctions > 0) {
                showAlert('info', 'Triggering lot discovery for all auctions...');
                
                // This would need to be implemented to trigger for all auctions
                // For now, just show a message
                showAlert('warning', 'Bulk lot discovery not yet implemented. Use individual auction triggers.');
            } else {
                showAlert('warning', 'No auctions found. Discover auctions first.');
            }
        } catch (error) {
            showAlert('danger', 'Error: ' + error.message);
        } finally {
            button.disabled = false;
            button.innerHTML = originalText;
        }
    }
    
    async function triggerAllBidCollection() {
        const button = event.target;
        const originalText = button.innerHTML;
        
        button.disabled = true;
        button.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Collecting...';
        
        try {
            // Get all lots first
            const statsResponse = await apiCall('/api/stats');
            if (statsResponse.total_lots > 0) {
                showAlert('info', 'Triggering bid collection for all lots...');
                
                // This would need to be implemented to trigger for all lots
                // For now, just show a message
                showAlert('warning', 'Bulk bid collection not yet implemented. Use individual lot triggers.');
            } else {
                showAlert('warning', 'No lots found. Discover lots first.');
            }
        } catch (error) {
            showAlert('danger', 'Error: ' + error.message);
        } finally {
            button.disabled = false;
            button.innerHTML = originalText;
        }
    }
    
    // Auto-refresh scheduled tasks every 10 seconds
    setInterval(async () => {
        try {
            const tasks = await apiCall('/api/scheduled-tasks');
            if (tasks && !tasks.error) {
                // Update task table without full page reload
                updateTaskTable(tasks);
            }
        } catch (error) {
            console.error('Error updating tasks:', error);
        }
    }, 10000);
    
    function updateTaskTable(tasks) {
        const tbody = document.querySelector('table tbody');
        if (!tbody || !tasks.length) return;
        
        // Simple update - in production would be more sophisticated
        const rows = tbody.querySelectorAll('tr');
        tasks.slice(0, rows.length).forEach((task, index) => {
            const row = rows[index];
            if (row) {
                const statusCell = row.querySelector('.badge');
                if (statusCell) {
                    statusCell.className = `badge ${getStatusBadgeClass(task.status)} status-badge`;
                    statusCell.textContent = task.status;
                }
            }
        });
    }
    
    function getStatusBadgeClass(status) {
        const classes = {
            'pending': 'bg-info',
            'running': 'bg-primary',
            'completed': 'bg-success',
            'failed': 'bg-danger'
        };
        return classes[status] || 'bg-secondary';
    }
</script>
{% endblock %}
