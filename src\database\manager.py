#!/usr/bin/env python3
"""
Database Manager for Auction Scraper System
Handles all database operations including CRUD operations for auctions, lots, and bids.
"""

import sqlite3
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from pathlib import Path
import json

from .models import (
    Auction, Lot, Bid, LifecycleLog, DatabaseSchema,
    datetime_to_string, string_to_datetime,
    serialize_images, deserialize_images
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatabaseManager:
    """Main database manager for auction scraper system"""
    
    def __init__(self, db_path: str = "data/auction_scraper.db"):
        self.db_path = db_path
        self._ensure_database_directory()
        self._initialize_database()
    
    def _ensure_database_directory(self):
        """Ensure database directory exists"""
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
    
    def _initialize_database(self):
        """Initialize database with required tables and indexes"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Create all tables and indexes
                for statement in DatabaseSchema.get_all_create_statements():
                    conn.execute(statement)
                conn.commit()
            logger.info("Database initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            raise
    
    def get_connection(self) -> sqlite3.Connection:
        """Get database connection with row factory"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    # Auction operations
    def save_auction(self, auction: Auction) -> bool:
        """Save or update auction in database"""
        try:
            with self.get_connection() as conn:
                auction.updated_at = datetime.now()
                
                conn.execute("""
                    INSERT OR REPLACE INTO auctions (
                        auction_id, title, start_time, end_time, lot_count,
                        location, status, lifecycle_stage, description,
                        created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    auction.auction_id,
                    auction.title,
                    datetime_to_string(auction.start_time),
                    datetime_to_string(auction.end_time),
                    auction.lot_count,
                    auction.location,
                    auction.status,
                    auction.lifecycle_stage,
                    auction.description,
                    datetime_to_string(auction.created_at),
                    datetime_to_string(auction.updated_at)
                ))
                conn.commit()
            return True
        except Exception as e:
            logger.error(f"Error saving auction {auction.auction_id}: {e}")
            return False
    
    def save_auctions(self, auctions: List[Auction]) -> bool:
        """Save multiple auctions in batch"""
        try:
            with self.get_connection() as conn:
                for auction in auctions:
                    auction.updated_at = datetime.now()
                    conn.execute("""
                        INSERT OR REPLACE INTO auctions (
                            auction_id, title, start_time, end_time, lot_count,
                            location, status, lifecycle_stage, description,
                            created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        auction.auction_id,
                        auction.title,
                        datetime_to_string(auction.start_time),
                        datetime_to_string(auction.end_time),
                        auction.lot_count,
                        auction.location,
                        auction.status,
                        auction.lifecycle_stage,
                        auction.description,
                        datetime_to_string(auction.created_at),
                        datetime_to_string(auction.updated_at)
                    ))
                conn.commit()
            logger.info(f"Saved {len(auctions)} auctions")
            return True
        except Exception as e:
            logger.error(f"Error saving auctions: {e}")
            return False
    
    def get_auction(self, auction_id: int) -> Optional[Auction]:
        """Get auction by ID"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM auctions WHERE auction_id = ?", (auction_id,))
                row = cursor.fetchone()
                
                if row:
                    return Auction(
                        auction_id=row['auction_id'],
                        title=row['title'],
                        start_time=string_to_datetime(row['start_time']),
                        end_time=string_to_datetime(row['end_time']),
                        lot_count=row['lot_count'],
                        location=row['location'],
                        status=row['status'],
                        lifecycle_stage=row['lifecycle_stage'],
                        description=row['description'],
                        created_at=string_to_datetime(row['created_at']),
                        updated_at=string_to_datetime(row['updated_at'])
                    )
                return None
        except Exception as e:
            logger.error(f"Error getting auction {auction_id}: {e}")
            return None
    
    def get_auctions(self, status: Optional[str] = None, limit: int = 100) -> List[Auction]:
        """Get auctions with optional status filter"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if status:
                    cursor.execute("""
                        SELECT * FROM auctions WHERE status = ? 
                        ORDER BY created_at DESC LIMIT ?
                    """, (status, limit))
                else:
                    cursor.execute("""
                        SELECT * FROM auctions 
                        ORDER BY created_at DESC LIMIT ?
                    """, (limit,))
                
                auctions = []
                for row in cursor.fetchall():
                    auction = Auction(
                        auction_id=row['auction_id'],
                        title=row['title'],
                        start_time=string_to_datetime(row['start_time']),
                        end_time=string_to_datetime(row['end_time']),
                        lot_count=row['lot_count'],
                        location=row['location'],
                        status=row['status'],
                        lifecycle_stage=row['lifecycle_stage'],
                        description=row['description'],
                        created_at=string_to_datetime(row['created_at']),
                        updated_at=string_to_datetime(row['updated_at'])
                    )
                    auctions.append(auction)
                
                return auctions
        except Exception as e:
            logger.error(f"Error getting auctions: {e}")
            return []
    
    # Lot operations
    def save_lot(self, lot: Lot) -> bool:
        """Save or update lot in database"""
        try:
            with self.get_connection() as conn:
                lot.updated_at = datetime.now()
                
                conn.execute("""
                    INSERT OR REPLACE INTO lots (
                        lot_id, auction_id, lot_number, title, description,
                        starting_price, current_price, closing_time, status,
                        bid_count, images, lifecycle_stage, bids_fetched,
                        location, condition, category, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    lot.lot_id,
                    lot.auction_id,
                    lot.lot_number,
                    lot.title,
                    lot.description,
                    lot.starting_price,
                    lot.current_price,
                    datetime_to_string(lot.closing_time),
                    lot.status,
                    lot.bid_count,
                    serialize_images(lot.images),
                    lot.lifecycle_stage,
                    lot.bids_fetched,
                    lot.location,
                    lot.condition,
                    lot.category,
                    datetime_to_string(lot.created_at),
                    datetime_to_string(lot.updated_at)
                ))
                conn.commit()
            return True
        except Exception as e:
            logger.error(f"Error saving lot {lot.lot_id}: {e}")
            return False
    
    def save_lots(self, lots: List[Lot]) -> bool:
        """Save multiple lots in batch"""
        try:
            with self.get_connection() as conn:
                for lot in lots:
                    lot.updated_at = datetime.now()
                    conn.execute("""
                        INSERT OR REPLACE INTO lots (
                            lot_id, auction_id, lot_number, title, description,
                            starting_price, current_price, closing_time, status,
                            bid_count, images, lifecycle_stage, bids_fetched,
                            location, condition, category, created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        lot.lot_id,
                        lot.auction_id,
                        lot.lot_number,
                        lot.title,
                        lot.description,
                        lot.starting_price,
                        lot.current_price,
                        datetime_to_string(lot.closing_time),
                        lot.status,
                        lot.bid_count,
                        serialize_images(lot.images),
                        lot.lifecycle_stage,
                        lot.bids_fetched,
                        lot.location,
                        lot.condition,
                        lot.category,
                        datetime_to_string(lot.created_at),
                        datetime_to_string(lot.updated_at)
                    ))
                conn.commit()
            logger.info(f"Saved {len(lots)} lots")
            return True
        except Exception as e:
            logger.error(f"Error saving lots: {e}")
            return False
    
    def get_lot(self, lot_id: int) -> Optional[Lot]:
        """Get lot by ID"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM lots WHERE lot_id = ?", (lot_id,))
                row = cursor.fetchone()
                
                if row:
                    return Lot(
                        lot_id=row['lot_id'],
                        auction_id=row['auction_id'],
                        lot_number=row['lot_number'],
                        title=row['title'],
                        description=row['description'],
                        starting_price=row['starting_price'],
                        current_price=row['current_price'],
                        closing_time=string_to_datetime(row['closing_time']),
                        status=row['status'],
                        bid_count=row['bid_count'],
                        images=deserialize_images(row['images']),
                        lifecycle_stage=row['lifecycle_stage'],
                        bids_fetched=bool(row['bids_fetched']),
                        location=row['location'],
                        condition=row['condition'],
                        category=row['category'],
                        created_at=string_to_datetime(row['created_at']),
                        updated_at=string_to_datetime(row['updated_at'])
                    )
                return None
        except Exception as e:
            logger.error(f"Error getting lot {lot_id}: {e}")
            return None
    
    def get_lots_by_auction(self, auction_id: int) -> List[Lot]:
        """Get all lots for an auction"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM lots WHERE auction_id = ? 
                    ORDER BY lot_number ASC
                """, (auction_id,))
                
                lots = []
                for row in cursor.fetchall():
                    lot = Lot(
                        lot_id=row['lot_id'],
                        auction_id=row['auction_id'],
                        lot_number=row['lot_number'],
                        title=row['title'],
                        description=row['description'],
                        starting_price=row['starting_price'],
                        current_price=row['current_price'],
                        closing_time=string_to_datetime(row['closing_time']),
                        status=row['status'],
                        bid_count=row['bid_count'],
                        images=deserialize_images(row['images']),
                        lifecycle_stage=row['lifecycle_stage'],
                        bids_fetched=bool(row['bids_fetched']),
                        location=row['location'],
                        condition=row['condition'],
                        category=row['category'],
                        created_at=string_to_datetime(row['created_at']),
                        updated_at=string_to_datetime(row['updated_at'])
                    )
                    lots.append(lot)
                
                return lots
        except Exception as e:
            logger.error(f"Error getting lots for auction {auction_id}: {e}")
            return []
    
    def get_upcoming_lots(self, hours: int = 24) -> List[Lot]:
        """Get lots closing within specified hours"""
        try:
            cutoff_time = datetime.now() + timedelta(hours=hours)
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM lots 
                    WHERE closing_time IS NOT NULL 
                    AND closing_time <= ? 
                    AND status = 'active'
                    ORDER BY closing_time ASC
                """, (datetime_to_string(cutoff_time),))
                
                lots = []
                for row in cursor.fetchall():
                    lot = Lot(
                        lot_id=row['lot_id'],
                        auction_id=row['auction_id'],
                        lot_number=row['lot_number'],
                        title=row['title'],
                        description=row['description'],
                        starting_price=row['starting_price'],
                        current_price=row['current_price'],
                        closing_time=string_to_datetime(row['closing_time']),
                        status=row['status'],
                        bid_count=row['bid_count'],
                        images=deserialize_images(row['images']),
                        lifecycle_stage=row['lifecycle_stage'],
                        bids_fetched=bool(row['bids_fetched']),
                        location=row['location'],
                        condition=row['condition'],
                        category=row['category'],
                        created_at=string_to_datetime(row['created_at']),
                        updated_at=string_to_datetime(row['updated_at'])
                    )
                    lots.append(lot)
                
                return lots
        except Exception as e:
            logger.error(f"Error getting upcoming lots: {e}")
            return []

    # Bid operations
    def save_bid(self, bid: Bid) -> bool:
        """Save bid in database"""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO bids (
                        lot_id, auction_id, amount, bid_date, bid_time,
                        timestamp, fetched_at, bidder_id
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    bid.lot_id,
                    bid.auction_id,
                    bid.amount,
                    bid.bid_date,
                    bid.bid_time,
                    datetime_to_string(bid.timestamp),
                    datetime_to_string(bid.fetched_at),
                    bid.bidder_id
                ))
                conn.commit()
            return True
        except Exception as e:
            logger.error(f"Error saving bid: {e}")
            return False

    def save_bids(self, bids: List[Bid]) -> bool:
        """Save multiple bids in batch"""
        try:
            with self.get_connection() as conn:
                for bid in bids:
                    conn.execute("""
                        INSERT OR REPLACE INTO bids (
                            lot_id, auction_id, amount, bid_date, bid_time,
                            timestamp, fetched_at, bidder_id
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        bid.lot_id,
                        bid.auction_id,
                        bid.amount,
                        bid.bid_date,
                        bid.bid_time,
                        datetime_to_string(bid.timestamp),
                        datetime_to_string(bid.fetched_at),
                        bid.bidder_id
                    ))
                conn.commit()
            logger.info(f"Saved {len(bids)} bids")
            return True
        except Exception as e:
            logger.error(f"Error saving bids: {e}")
            return False

    def get_bids_for_lot(self, lot_id: int) -> List[Bid]:
        """Get all bids for a lot"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM bids WHERE lot_id = ?
                    ORDER BY timestamp ASC
                """, (lot_id,))

                bids = []
                for row in cursor.fetchall():
                    bid = Bid(
                        bid_id=row['bid_id'],
                        lot_id=row['lot_id'],
                        auction_id=row['auction_id'],
                        amount=row['amount'],
                        bid_date=row['bid_date'],
                        bid_time=row['bid_time'],
                        timestamp=string_to_datetime(row['timestamp']),
                        fetched_at=string_to_datetime(row['fetched_at']),
                        bidder_id=row['bidder_id']
                    )
                    bids.append(bid)

                return bids
        except Exception as e:
            logger.error(f"Error getting bids for lot {lot_id}: {e}")
            return []

    # Lifecycle log operations
    def log_lifecycle_action(self, auction_id: Optional[int], lot_id: Optional[int],
                           action: str, status: str, details: str = "") -> bool:
        """Log lifecycle action"""
        try:
            with self.get_connection() as conn:
                log_entry = LifecycleLog(
                    auction_id=auction_id,
                    lot_id=lot_id,
                    action=action,
                    status=status,
                    details=details
                )

                conn.execute("""
                    INSERT INTO lifecycle_log (
                        auction_id, lot_id, action, status, timestamp, details
                    ) VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    log_entry.auction_id,
                    log_entry.lot_id,
                    log_entry.action,
                    log_entry.status,
                    datetime_to_string(log_entry.timestamp),
                    log_entry.details
                ))
                conn.commit()
            return True
        except Exception as e:
            logger.error(f"Error logging lifecycle action: {e}")
            return False

    def get_lifecycle_logs(self, limit: int = 100) -> List[LifecycleLog]:
        """Get recent lifecycle logs"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM lifecycle_log
                    ORDER BY timestamp DESC LIMIT ?
                """, (limit,))

                logs = []
                for row in cursor.fetchall():
                    log = LifecycleLog(
                        log_id=row['log_id'],
                        auction_id=row['auction_id'],
                        lot_id=row['lot_id'],
                        action=row['action'],
                        status=row['status'],
                        timestamp=string_to_datetime(row['timestamp']),
                        details=row['details']
                    )
                    logs.append(log)

                return logs
        except Exception as e:
            logger.error(f"Error getting lifecycle logs: {e}")
            return []

    # Statistics and reporting
    def get_statistics(self) -> Dict:
        """Get database statistics"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # Count auctions
                cursor.execute("SELECT COUNT(*) FROM auctions")
                total_auctions = cursor.fetchone()[0]

                # Count lots
                cursor.execute("SELECT COUNT(*) FROM lots")
                total_lots = cursor.fetchone()[0]

                # Count bids
                cursor.execute("SELECT COUNT(*) FROM bids")
                total_bids = cursor.fetchone()[0]

                # Count active lots
                cursor.execute("SELECT COUNT(*) FROM lots WHERE status = 'active'")
                active_lots = cursor.fetchone()[0]

                # Count lots with bids
                cursor.execute("SELECT COUNT(DISTINCT lot_id) FROM bids")
                lots_with_bids = cursor.fetchone()[0]

                return {
                    'total_auctions': total_auctions,
                    'total_lots': total_lots,
                    'total_bids': total_bids,
                    'active_lots': active_lots,
                    'lots_with_bids': lots_with_bids
                }
        except Exception as e:
            logger.error(f"Error getting statistics: {e}")
            return {}

    def get_auction_tree(self) -> List[Dict]:
        """Get auction tree structure for navigation"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT a.auction_id, a.title, a.lot_count, a.status,
                           COUNT(l.lot_id) as actual_lots
                    FROM auctions a
                    LEFT JOIN lots l ON a.auction_id = l.auction_id
                    GROUP BY a.auction_id
                    ORDER BY a.created_at DESC
                """)

                tree = []
                for row in cursor.fetchall():
                    auction_node = {
                        'auction_id': row['auction_id'],
                        'title': row['title'],
                        'lot_count': row['lot_count'],
                        'actual_lots': row['actual_lots'],
                        'status': row['status'],
                        'type': 'auction'
                    }
                    tree.append(auction_node)

                return tree
        except Exception as e:
            logger.error(f"Error getting auction tree: {e}")
            return []
