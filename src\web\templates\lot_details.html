{% extends "base.html" %}

{% block title %}Lot {{ lot.lot_number }} - Auction Scraper{% endblock %}

{% block content %}
<div class="col-12">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('auctions_list') }}">Auctions</a></li>
            {% if auction %}
            <li class="breadcrumb-item"><a href="{{ url_for('auction_details', auction_id=auction.auction_id) }}">Auction {{ auction.auction_id }}</a></li>
            {% endif %}
            <li class="breadcrumb-item active">Lot {{ lot.lot_number }}</li>
        </ol>
    </nav>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-box"></i> Lot {{ lot.lot_number }}: {{ lot.title }}</h1>
        <div class="btn-group">
            <button class="btn btn-primary" onclick="triggerBidCollection({{ lot.lot_id }})">
                <i class="bi bi-currency-euro"></i> Collect Bids
            </button>
            {% if auction %}
            <a href="{{ url_for('auction_details', auction_id=auction.auction_id) }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Auction
            </a>
            {% endif %}
        </div>
    </div>

    <div class="row">
        <!-- Lot Information -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="bi bi-info-circle"></i> Lot Information</h5>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-3">Lot ID:</dt>
                        <dd class="col-sm-9">{{ lot.lot_id }}</dd>
                        
                        <dt class="col-sm-3">Lot Number:</dt>
                        <dd class="col-sm-9"><strong>{{ lot.lot_number }}</strong></dd>
                        
                        <dt class="col-sm-3">Title:</dt>
                        <dd class="col-sm-9">{{ lot.title }}</dd>
                        
                        <dt class="col-sm-3">Current Price:</dt>
                        <dd class="col-sm-9">
                            <h4 class="text-success">{{ lot.current_price | currency }}</h4>
                        </dd>
                        
                        <dt class="col-sm-3">Starting Price:</dt>
                        <dd class="col-sm-9">{{ lot.starting_price | currency }}</dd>
                        
                        <dt class="col-sm-3">Closing Time:</dt>
                        <dd class="col-sm-9">
                            {% if lot.closing_time %}
                                {{ lot.closing_time | datetime_format('%Y-%m-%d %H:%M:%S') }}
                                {% set now = moment() %}
                                {% if lot.closing_time < now %}
                                    <span class="badge bg-danger ms-2">Closed</span>
                                {% elif lot.closing_time < now + timedelta(minutes=5) %}
                                    <span class="badge bg-warning ms-2 closing-soon">Closing Soon!</span>
                                {% elif lot.closing_time < now + timedelta(hours=1) %}
                                    <span class="badge bg-info ms-2">Closing Today</span>
                                {% endif %}
                            {% else %}
                                <span class="text-muted">Not specified</span>
                            {% endif %}
                        </dd>
                        
                        <dt class="col-sm-3">Status:</dt>
                        <dd class="col-sm-9">
                            <span class="badge {{ lot.status | status_badge }}">{{ lot.status }}</span>
                        </dd>
                        
                        <dt class="col-sm-3">Lifecycle Stage:</dt>
                        <dd class="col-sm-9">
                            <span class="badge {{ lot.lifecycle_stage | status_badge }} status-badge">
                                {{ lot.lifecycle_stage.replace('_', ' ').title() }}
                            </span>
                        </dd>
                        
                        <dt class="col-sm-3">Total Bids:</dt>
                        <dd class="col-sm-9">
                            <span class="badge bg-info">{{ lot.bid_count }}</span>
                            {% if lot.bids_fetched %}
                                <span class="badge bg-success ms-1"><i class="bi bi-check"></i> Fetched</span>
                            {% else %}
                                <span class="badge bg-warning ms-1"><i class="bi bi-clock"></i> Pending</span>
                            {% endif %}
                        </dd>
                        
                        {% if lot.location %}
                        <dt class="col-sm-3">Location:</dt>
                        <dd class="col-sm-9"><i class="bi bi-geo-alt"></i> {{ lot.location }}</dd>
                        {% endif %}
                        
                        {% if lot.condition %}
                        <dt class="col-sm-3">Condition:</dt>
                        <dd class="col-sm-9">{{ lot.condition }}</dd>
                        {% endif %}
                        
                        {% if lot.category %}
                        <dt class="col-sm-3">Category:</dt>
                        <dd class="col-sm-9">{{ lot.category }}</dd>
                        {% endif %}
                    </dl>
                    
                    {% if lot.description %}
                    <hr>
                    <h6>Description:</h6>
                    <p>{{ lot.description }}</p>
                    {% endif %}
                </div>
            </div>

            <!-- Images -->
            {% if lot.images %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="bi bi-images"></i> Images ({{ lot.images|length }})</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for image in lot.images %}
                        <div class="col-md-4 mb-3">
                            <img src="{{ image }}" class="img-fluid rounded" alt="Lot Image" 
                                 style="cursor: pointer;" onclick="showImageModal('{{ image }}')">
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Quick Stats -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="bi bi-graph-up"></i> Quick Stats</h5>
                </div>
                <div class="card-body text-center">
                    <div class="row">
                        <div class="col-6">
                            <h4 class="text-primary">{{ lot.bid_count }}</h4>
                            <small class="text-muted">Total Bids</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ lot.current_price | currency }}</h4>
                            <small class="text-muted">Current Price</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Timeline -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="bi bi-clock"></i> Timeline</h5>
                </div>
                <div class="card-body">
                    <small class="text-muted">Created:</small><br>
                    {{ lot.created_at | datetime_format('%Y-%m-%d %H:%M') }}<br><br>
                    
                    <small class="text-muted">Last Updated:</small><br>
                    {{ lot.updated_at | datetime_format('%Y-%m-%d %H:%M') }}<br>
                    <small class="text-muted">({{ lot.updated_at | time_ago }})</small>
                </div>
            </div>

            <!-- Actions -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-tools"></i> Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" onclick="triggerBidCollection({{ lot.lot_id }})">
                            <i class="bi bi-currency-euro"></i> Collect Bids
                        </button>
                        {% if auction %}
                        <a href="{{ url_for('auction_details', auction_id=auction.auction_id) }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Auction
                        </a>
                        {% endif %}
                        <a href="{{ url_for('timetable') }}" class="btn btn-outline-info">
                            <i class="bi bi-calendar"></i> View Timetable
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bid History -->
    <div class="card mt-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5><i class="bi bi-currency-euro"></i> Bid History ({{ bids|length }})</h5>
            <button class="btn btn-sm btn-primary" onclick="triggerBidCollection({{ lot.lot_id }})">
                <i class="bi bi-arrow-clockwise"></i> Refresh Bids
            </button>
        </div>
        <div class="card-body">
            {% if bids %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Amount</th>
                                <th>Date</th>
                                <th>Time</th>
                                <th>Timestamp</th>
                                <th>Fetched At</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for bid in bids %}
                            <tr>
                                <td>
                                    <strong class="text-success">{{ bid.amount | currency }}</strong>
                                </td>
                                <td>{{ bid.bid_date }}</td>
                                <td>{{ bid.bid_time }}</td>
                                <td>
                                    {% if bid.timestamp %}
                                        {{ bid.timestamp | datetime_format('%Y-%m-%d %H:%M:%S') }}
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {% if bid.fetched_at %}
                                            {{ bid.fetched_at | time_ago }}
                                        {% else %}
                                            N/A
                                        {% endif %}
                                    </small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center text-muted py-5">
                    <i class="bi bi-currency-euro fs-1"></i>
                    <h4 class="mt-3">No Bids Found</h4>
                    <p>No bid history has been collected for this lot yet.</p>
                    <button class="btn btn-primary" onclick="triggerBidCollection({{ lot.lot_id }})">
                        <i class="bi bi-currency-euro"></i> Collect Bids
                    </button>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Lot Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" class="img-fluid" alt="Lot Image">
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function showImageModal(imageSrc) {
        document.getElementById('modalImage').src = imageSrc;
        new bootstrap.Modal(document.getElementById('imageModal')).show();
    }
    
    // Add closing soon animation if applicable
    document.addEventListener('DOMContentLoaded', function() {
        const closingSoonBadges = document.querySelectorAll('.closing-soon');
        closingSoonBadges.forEach(badge => {
            badge.style.animation = 'pulse 2s infinite';
        });
    });
</script>
{% endblock %}
