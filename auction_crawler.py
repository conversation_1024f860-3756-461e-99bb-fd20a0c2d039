#!/usr/bin/env python3
"""
Aurena Auction Crawler - Clean, Streamlined Version
Extracts real auction and lot data from Aurena auction site.
"""

import requests
from bs4 import BeautifulSoup
import re
import json
import sqlite3
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging
from dataclasses import dataclass

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class Auction:
    """Auction data structure"""
    auction_id: int
    title: str
    start_time: datetime
    end_time: datetime
    status: str
    lot_count: int
    location: str = ""
    description: str = ""

@dataclass
class Lot:
    """Lot data structure"""
    lot_id: int
    auction_id: int
    lot_number: int
    title: str
    description: str
    starting_price: int
    current_price: int
    closing_time: datetime
    status: str
    bid_count: int
    images: List[str]
    category: str = ""
    condition: str = ""
    location: str = ""

class AuctionCrawler:
    """Main auction crawler class"""
    
    def __init__(self, db_path: str = "aurena_auctions.db"):
        self.db_path = db_path
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.init_database()
    
    def init_database(self):
        """Initialize the database with required tables"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Create auctions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS auctions (
                    auction_id INTEGER PRIMARY KEY,
                    title TEXT NOT NULL,
                    start_time TEXT NOT NULL,
                    end_time TEXT NOT NULL,
                    status TEXT NOT NULL,
                    lot_count INTEGER DEFAULT 0,
                    location TEXT DEFAULT '',
                    description TEXT DEFAULT '',
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            """)
            
            # Create lots table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS lots (
                    lot_id INTEGER PRIMARY KEY,
                    auction_id INTEGER NOT NULL,
                    lot_number INTEGER NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT DEFAULT '',
                    starting_price INTEGER DEFAULT 0,
                    current_price INTEGER DEFAULT 0,
                    closing_time TEXT NOT NULL,
                    status TEXT NOT NULL,
                    bid_count INTEGER DEFAULT 0,
                    images TEXT DEFAULT '[]',
                    category TEXT DEFAULT '',
                    condition TEXT DEFAULT '',
                    location TEXT DEFAULT '',
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    FOREIGN KEY (auction_id) REFERENCES auctions (auction_id)
                )
            """)
            
            conn.commit()
            logger.info("Database initialized successfully")
    
    def fetch_active_auctions(self) -> List[Auction]:
        """Fetch list of active auctions from Aurena API"""
        try:
            url = "https://www.aurena.at/api/auctions"
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            auctions = []
            
            for auction_data in data:
                try:
                    auction = Auction(
                        auction_id=auction_data['id'],
                        title=auction_data.get('title', ''),
                        start_time=datetime.fromtimestamp(auction_data.get('start', 0) / 1000),
                        end_time=datetime.fromtimestamp(auction_data.get('end', 0) / 1000),
                        status=auction_data.get('status', 'unknown'),
                        lot_count=auction_data.get('lots', 0),
                        location=auction_data.get('location', ''),
                        description=auction_data.get('description', '')
                    )
                    auctions.append(auction)
                except Exception as e:
                    logger.warning(f"Error parsing auction {auction_data.get('id', 'unknown')}: {e}")
                    continue
            
            logger.info(f"Fetched {len(auctions)} auctions")
            return auctions
            
        except Exception as e:
            logger.error(f"Error fetching auctions: {e}")
            return []
    
    def extract_lots_from_auction(self, auction_id: int) -> List[Lot]:
        """Extract all lots from an auction by parsing the embedded JavaScript data"""
        auction_url = f'https://www.aurena.at/auktion/{auction_id}'
        logger.info(f"Extracting lots from auction {auction_id}")
        
        try:
            response = self.session.get(auction_url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find the script containing lot data
            lot_data_script = None
            scripts = soup.find_all('script')
            
            for script in scripts:
                if script.string and ('Van Ford Galaxy' in script.string or '"lid":' in script.string):
                    lot_data_script = script.string
                    break
            
            if not lot_data_script:
                logger.warning(f"No lot data script found for auction {auction_id}")
                return []
            
            # Parse the embedded lot data
            lots = self._parse_lot_data_from_script(lot_data_script, auction_id)
            logger.info(f"Successfully extracted {len(lots)} lots from auction {auction_id}")
            
            return lots
            
        except Exception as e:
            logger.error(f"Error extracting lots from auction {auction_id}: {e}")
            return []
    
    def _parse_lot_data_from_script(self, script_content: str, auction_id: int) -> List[Lot]:
        """Parse lot data from the JavaScript content"""
        lots = []
        
        try:
            # Decode HTML entities in the JavaScript
            content = script_content.replace('&q;', '"').replace('&l;', '<').replace('&g;', '>')
            
            # Find lot objects using the pattern: {"seq":N,"et":timestamp,"lid":id,...}
            logger.debug("Looking for lot objects with seq/et/lid pattern...")
            
            # Find all positions where lot objects start
            lot_starts = []
            for match in re.finditer(r'\{"seq":\d+,"et":\d+,"lid":\d+', content):
                lot_starts.append(match.start())
            
            logger.debug(f"Found {len(lot_starts)} potential lot object starts")
            
            lot_objects = []
            for start_pos in lot_starts:
                # Find the complete lot object by counting braces
                brace_count = 0
                end_pos = start_pos
                
                for i in range(start_pos, len(content)):
                    if content[i] == '{':
                        brace_count += 1
                    elif content[i] == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            end_pos = i + 1
                            break
                
                if end_pos > start_pos:
                    lot_obj = content[start_pos:end_pos]
                    lot_objects.append(lot_obj)
            
            logger.debug(f"Extracted {len(lot_objects)} complete lot objects")
            
            for i, lot_obj_str in enumerate(lot_objects):
                try:
                    lot_data = self._extract_lot_fields(lot_obj_str, auction_id, i + 1)
                    if lot_data:
                        lots.append(lot_data)
                except Exception as e:
                    logger.warning(f"Error parsing lot {i+1}: {e}")
                    continue
            
        except Exception as e:
            logger.error(f"Error parsing script content: {e}")
        
        return lots
    
    def _extract_lot_fields(self, lot_obj_str: str, auction_id: int, lot_number: int) -> Optional[Lot]:
        """Extract individual lot fields from a lot object string"""
        try:
            # Extract lot ID
            lot_id = None
            for pattern in [r'"lid":(\d+)', r'lid.*?(\d+)', r'"id":(\d+)']:
                lid_match = re.search(pattern, lot_obj_str)
                if lid_match:
                    lot_id = int(lid_match.group(1))
                    break
            
            # Extract sequence number (lot number)
            sequence = lot_number
            seq_match = re.search(r'"seq":(\d+)', lot_obj_str)
            if seq_match:
                sequence = int(seq_match.group(1))
            
            # Extract title
            title = f"Lot {sequence}"
            title_patterns = [
                r'"ti":\{"de_DE":"([^"]+)"',
                r'ti.*?de_DE.*?"([^"]+)"',
                r'"title":"([^"]+)"'
            ]
            for pattern in title_patterns:
                title_match = re.search(pattern, lot_obj_str)
                if title_match:
                    title = title_match.group(1)
                    break
            
            # Extract description
            description = ""
            desc_patterns = [
                r'"de":\{"de_DE":"([^"]+)"',
                r'de.*?de_DE.*?"([^"]+)"',
                r'"description":"([^"]+)"'
            ]
            for pattern in desc_patterns:
                desc_match = re.search(pattern, lot_obj_str)
                if desc_match:
                    raw_desc = desc_match.group(1)
                    # Clean HTML tags
                    description = re.sub(r'<[^>]+>', ' ', raw_desc).strip()
                    description = re.sub(r'\s+', ' ', description)  # Normalize whitespace
                    break
            
            # Extract closing time
            closing_time = None
            et_patterns = [r'"et":(\d+)', r'et.*?(\d+)', r'"endTime":(\d+)']
            for pattern in et_patterns:
                et_match = re.search(pattern, lot_obj_str)
                if et_match:
                    timestamp = int(et_match.group(1))
                    closing_time = datetime.fromtimestamp(timestamp / 1000)
                    break
            
            # Extract current bid - prices are in whole euros
            current_bid = 0
            bid_patterns = [
                r'"hib":\{"val":(\d+)',
                r'hib.*?val.*?(\d+)',
                r'"currentBid":(\d+)',
                r'"highestBid":(\d+)'
            ]
            for pattern in bid_patterns:
                hib_match = re.search(pattern, lot_obj_str)
                if hib_match:
                    # Convert cents to euros and round to whole euros
                    current_bid = round(int(hib_match.group(1)) / 100)
                    break
            
            # Extract starting bid
            starting_bid = 0
            start_patterns = [
                r'"startingBid":(\d+)',
                r'"minBid":(\d+)',
                r'"reservePrice":(\d+)'
            ]
            for pattern in start_patterns:
                start_match = re.search(pattern, lot_obj_str)
                if start_match:
                    # Convert cents to euros and round to whole euros
                    starting_bid = round(int(start_match.group(1)) / 100)
                    break
            
            # Extract images
            images = re.findall(r'"(https://aurena\.s3\.amazonaws\.com/[^"]+)"', lot_obj_str)
            
            # Extract status
            status = "active"
            if '"status"' in lot_obj_str:
                status_match = re.search(r'"status":"([^"]+)"', lot_obj_str)
                if status_match:
                    status = status_match.group(1)
            
            lot = Lot(
                lot_id=lot_id,
                auction_id=auction_id,
                lot_number=sequence,
                title=title,
                description=description,
                starting_price=starting_bid,
                current_price=current_bid,
                closing_time=closing_time,
                status=status,
                bid_count=0,  # Would need additional parsing to get actual bid count
                images=images[:5],  # Limit to first 5 images
                category='',
                condition='',
                location=''
            )
            
            logger.debug(f"Extracted lot: {title} (ID: {lot_id}, Price: €{current_bid})")
            return lot

        except Exception as e:
            logger.error(f"Error extracting lot fields: {e}")
            return None

    def save_auctions(self, auctions: List[Auction]) -> bool:
        """Save auctions to database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                current_time = datetime.now().isoformat()

                for auction in auctions:
                    cursor.execute("""
                        INSERT OR REPLACE INTO auctions (
                            auction_id, title, start_time, end_time, status,
                            lot_count, location, description, created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        auction.auction_id, auction.title, auction.start_time.isoformat(),
                        auction.end_time.isoformat(), auction.status, auction.lot_count,
                        auction.location, auction.description, current_time, current_time
                    ))

                conn.commit()
                logger.info(f"Successfully saved {len(auctions)} auctions to database")
                return True

        except Exception as e:
            logger.error(f"Failed to save auctions: {e}")
            return False

    def save_lots(self, lots: List[Lot]) -> bool:
        """Save lots to database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                current_time = datetime.now().isoformat()

                for lot in lots:
                    cursor.execute("""
                        INSERT OR REPLACE INTO lots (
                            lot_id, auction_id, lot_number, title, description,
                            starting_price, current_price, closing_time, status,
                            bid_count, images, category, condition, location,
                            created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        lot.lot_id, lot.auction_id, lot.lot_number, lot.title,
                        lot.description, lot.starting_price, lot.current_price,
                        lot.closing_time.isoformat() if lot.closing_time else '',
                        lot.status, lot.bid_count, json.dumps(lot.images),
                        lot.category, lot.condition, lot.location,
                        current_time, current_time
                    ))

                conn.commit()
                logger.info(f"Successfully saved {len(lots)} lots to database")
                return True

        except Exception as e:
            logger.error(f"Failed to save lots: {e}")
            return False

    def get_database_stats(self) -> Dict:
        """Get database statistics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Get auction count
                cursor.execute("SELECT COUNT(*) FROM auctions")
                auction_count = cursor.fetchone()[0]

                # Get lot count
                cursor.execute("SELECT COUNT(*) FROM lots")
                lot_count = cursor.fetchone()[0]

                # Get lots with bids
                cursor.execute("SELECT COUNT(*) FROM lots WHERE current_price > 0")
                lots_with_bids = cursor.fetchone()[0]

                # Get upcoming lots (closing in next 24 hours)
                tomorrow = (datetime.now() + timedelta(days=1)).isoformat()
                cursor.execute("SELECT COUNT(*) FROM lots WHERE closing_time <= ? AND closing_time > ?",
                             (tomorrow, datetime.now().isoformat()))
                upcoming_lots = cursor.fetchone()[0]

                return {
                    'total_auctions': auction_count,
                    'total_lots': lot_count,
                    'lots_with_bids': lots_with_bids,
                    'upcoming_lots': upcoming_lots
                }

        except Exception as e:
            logger.error(f"Error getting database stats: {e}")
            return {}

    def crawl_all_auctions(self) -> bool:
        """Main method to crawl all auctions and their lots"""
        logger.info("Starting auction crawl...")

        # Fetch active auctions
        auctions = self.fetch_active_auctions()
        if not auctions:
            logger.warning("No auctions found")
            return False

        # Save auctions to database
        if not self.save_auctions(auctions):
            logger.error("Failed to save auctions")
            return False

        # Extract lots from each auction
        all_lots = []
        for auction in auctions:
            logger.info(f"Processing auction {auction.auction_id}: {auction.title}")
            lots = self.extract_lots_from_auction(auction.auction_id)
            all_lots.extend(lots)

        # Save all lots to database
        if all_lots:
            if not self.save_lots(all_lots):
                logger.error("Failed to save lots")
                return False

        # Print statistics
        stats = self.get_database_stats()
        logger.info(f"Crawl completed successfully!")
        logger.info(f"Statistics: {stats}")

        return True

def main():
    """Main function for testing"""
    crawler = AuctionCrawler()

    # Test with specific auction 14941
    logger.info("Testing with auction 14941...")
    lots = crawler.extract_lots_from_auction(14941)

    if lots:
        logger.info(f"Successfully extracted {len(lots)} lots")

        # Find Van Ford Galaxy
        ford_lot = None
        for lot in lots:
            if 'Van Ford Galaxy' in lot.title:
                ford_lot = lot
                break

        if ford_lot:
            print(f"\n=== VAN FORD GALAXY LOT ===")
            print(f"Lot ID: {ford_lot.lot_id}")
            print(f"Title: {ford_lot.title}")
            print(f"Current Price: €{ford_lot.current_price}")
            print(f"Closing Time: {ford_lot.closing_time}")
            print(f"Description: {ford_lot.description}")
            print(f"Images: {len(ford_lot.images)} found")

        # Save to database
        crawler.save_lots(lots)

        # Show stats
        stats = crawler.get_database_stats()
        print(f"\nDatabase Stats: {stats}")
    else:
        logger.error("No lots extracted")

if __name__ == "__main__":
    main()
