<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scraping Control - Aurena Auction Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .nav {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .nav a {
            text-decoration: none;
            color: #667eea;
            margin-right: 20px;
            font-weight: 500;
        }
        .nav a:hover {
            color: #764ba2;
        }
        .control-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .control-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .control-card h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .action-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: transform 0.2s;
        }
        .action-button:hover {
            transform: translateY(-2px);
        }
        .action-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .lifecycle-stages {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        .stage-badge {
            background: #e9ecef;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            color: #495057;
        }
        .stage-badge.discovered { background: #fff3cd; color: #856404; }
        .stage-badge.lots_fetched { background: #d4edda; color: #155724; }
        .stage-badge.bids_fetched { background: #d1ecf1; color: #0c5460; }
        .log-container {
            max-height: 400px;
            overflow-y: auto;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }
        .log-entry {
            padding: 8px;
            border-bottom: 1px solid #dee2e6;
            font-size: 12px;
        }
        .log-entry:last-child {
            border-bottom: none;
        }
        .log-timestamp {
            color: #6c757d;
            font-weight: bold;
        }
        .log-action {
            color: #495057;
            margin-left: 10px;
        }
        .log-status {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            margin-left: 10px;
        }
        .log-status.success { background: #d4edda; color: #155724; }
        .log-status.error { background: #f8d7da; color: #721c24; }
        .log-status.no_data { background: #fff3cd; color: #856404; }
        .message {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            display: none;
        }
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .workflow-steps {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .workflow-step {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
        }
        .step-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }
        .step-description {
            flex: 1;
        }
        .step-action {
            margin-left: 15px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 Scraping Control Dashboard</h1>
        <p>Monitor and control the auction lifecycle scraping process</p>
    </div>

    <div class="nav">
        <a href="/">🏠 Dashboard</a>
        <a href="/timetable">📅 Timetable</a>
        <a href="/scraping-control">🔧 Scraping Control</a>
    </div>

    <div class="stats-grid">
        <div class="stat-item">
            <div class="stat-number">{{ total_auctions }}</div>
            <div class="stat-label">Total Auctions</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">{{ total_lots }}</div>
            <div class="stat-label">Total Lots</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">{{ total_bids }}</div>
            <div class="stat-label">Total Bids</div>
        </div>
    </div>

    <div class="control-grid">
        <div class="control-card">
            <h3>🔍 Lifecycle Workflow Control</h3>
            <div class="workflow-steps">
                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div class="step-description">
                        <strong>Discover Auctions</strong><br>
                        Fetch all auctions with starting time and lot count
                    </div>
                    <div class="step-action">
                        <button class="action-button" onclick="discoverAuctions()">
                            🔍 Discover
                        </button>
                    </div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div class="step-description">
                        <strong>Fetch Lot Details</strong><br>
                        Get detailed lot information when prompted
                    </div>
                    <div class="step-action">
                        <input type="number" id="auctionId" placeholder="Auction ID" style="width: 100px; margin-right: 5px;">
                        <button class="action-button" onclick="fetchLots()">
                            📦 Fetch Lots
                        </button>
                    </div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div class="step-description">
                        <strong>Fetch Bid History</strong><br>
                        Get offer history when prompted or before closing
                    </div>
                    <div class="step-action">
                        <input type="number" id="lotId" placeholder="Lot ID" style="width: 100px; margin-right: 5px;">
                        <button class="action-button" onclick="fetchBids()">
                            💰 Fetch Bids
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="control-card">
            <h3>📊 Lifecycle Stages</h3>
            <div>
                <h4>Auction Stages:</h4>
                <div class="lifecycle-stages">
                    {% for stage, count in auction_stages.items() %}
                    <span class="stage-badge {{ stage }}">{{ stage }}: {{ count }}</span>
                    {% endfor %}
                </div>
                
                <h4>Lot Stages:</h4>
                <div class="lifecycle-stages">
                    {% for stage, count in lot_stages.items() %}
                    <span class="stage-badge {{ stage }}">{{ stage }}: {{ count }}</span>
                    {% endfor %}
                </div>
            </div>
        </div>

        <div class="control-card">
            <h3>📝 Recent Actions</h3>
            <div class="log-container">
                {% for action in recent_actions %}
                <div class="log-entry">
                    <span class="log-timestamp">{{ action.timestamp[:19] }}</span>
                    <span class="log-action">{{ action.action }}</span>
                    <span class="log-status {{ action.status }}">{{ action.status }}</span>
                    {% if action.details %}
                    <div style="margin-left: 20px; color: #6c757d; font-size: 11px;">
                        {{ action.details }}
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <div id="message" class="message"></div>

    <script>
        function showMessage(text, type = 'success') {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = text;
            messageDiv.className = `message ${type}`;
            messageDiv.style.display = 'block';
            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 5000);
        }

        async function discoverAuctions() {
            const button = event.target;
            button.disabled = true;
            button.textContent = '🔄 Discovering...';
            
            try {
                const response = await fetch('/api/discover-auctions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showMessage(result.message, 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showMessage(result.message, 'error');
                }
            } catch (error) {
                showMessage('Error: ' + error.message, 'error');
            } finally {
                button.disabled = false;
                button.textContent = '🔍 Discover';
            }
        }

        async function fetchLots() {
            const auctionId = document.getElementById('auctionId').value;
            if (!auctionId) {
                showMessage('Please enter an auction ID', 'error');
                return;
            }
            
            const button = event.target;
            button.disabled = true;
            button.textContent = '🔄 Fetching...';
            
            try {
                const response = await fetch(`/api/fetch-lots/${auctionId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ reason: 'dashboard_control' })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showMessage(result.message, 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showMessage(result.message, 'error');
                }
            } catch (error) {
                showMessage('Error: ' + error.message, 'error');
            } finally {
                button.disabled = false;
                button.textContent = '📦 Fetch Lots';
            }
        }

        async function fetchBids() {
            const lotId = document.getElementById('lotId').value;
            if (!lotId) {
                showMessage('Please enter a lot ID', 'error');
                return;
            }
            
            const button = event.target;
            button.disabled = true;
            button.textContent = '🔄 Fetching...';
            
            try {
                const response = await fetch(`/api/fetch-bids/${lotId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ reason: 'dashboard_control' })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showMessage(result.message, 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showMessage(result.message, 'error');
                }
            } catch (error) {
                showMessage('Error: ' + error.message, 'error');
            } finally {
                button.disabled = false;
                button.textContent = '💰 Fetch Bids';
            }
        }

        // Auto-refresh every 30 seconds
        setInterval(() => {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
