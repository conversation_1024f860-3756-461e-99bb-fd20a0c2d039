#!/usr/bin/env python3
"""
Comprehensive Auction Discovery System
Discovers many more real auctions from Aurena by scanning multiple sources
"""

import requests
import sqlite3
from bs4 import BeautifulSoup
import re
import time
from datetime import datetime, timedelta
from typing import List, Dict, Set
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ComprehensiveAuctionDiscovery:
    """Discovers many more real auctions from Aurena"""
    
    def __init__(self, db_path: str = "aurena_auctions.db"):
        self.db_path = db_path
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.base_url = "https://www.aurena.at"
        self.discovered_auction_ids: Set[int] = set()
    
    def discover_all_auctions(self) -> List[Dict]:
        """Comprehensive auction discovery from multiple sources"""
        logger.info("🔍 Starting comprehensive auction discovery...")
        
        all_auctions = []
        
        # Method 1: Scan auction ID ranges
        auctions_from_ranges = self._scan_auction_id_ranges()
        all_auctions.extend(auctions_from_ranges)
        
        # Method 2: Parse main auction list pages
        auctions_from_pages = self._parse_auction_list_pages()
        all_auctions.extend(auctions_from_pages)
        
        # Method 3: Follow category links
        auctions_from_categories = self._discover_from_categories()
        all_auctions.extend(auctions_from_categories)
        
        # Remove duplicates
        unique_auctions = self._remove_duplicates(all_auctions)
        
        # Save to database
        self._save_discovered_auctions(unique_auctions)
        
        logger.info(f"✅ Comprehensive discovery complete: {len(unique_auctions)} unique auctions found")
        return unique_auctions
    
    def _scan_auction_id_ranges(self) -> List[Dict]:
        """Scan ranges of auction IDs to find active auctions"""
        logger.info("📊 Scanning auction ID ranges...")
        
        auctions = []
        
        # Scan recent auction IDs (last 500 IDs from known working ones)
        base_ids = [14941, 15096, 15099, 15100, 15101, 15102]
        
        for base_id in base_ids:
            # Scan backwards and forwards from known IDs
            for offset in range(-50, 51):  # 100 IDs around each known ID
                auction_id = base_id + offset
                
                if auction_id in self.discovered_auction_ids:
                    continue
                
                try:
                    auction_data = self._check_auction_exists(auction_id)
                    if auction_data:
                        auctions.append(auction_data)
                        self.discovered_auction_ids.add(auction_id)
                        logger.info(f"Found auction {auction_id}: {auction_data['title'][:40]}...")
                    
                    # Rate limiting
                    time.sleep(0.5)
                    
                except Exception as e:
                    logger.debug(f"Error checking auction {auction_id}: {e}")
                    continue
        
        logger.info(f"📊 ID range scan found {len(auctions)} auctions")
        return auctions
    
    def _parse_auction_list_pages(self) -> List[Dict]:
        """Parse main auction list pages"""
        logger.info("📄 Parsing auction list pages...")
        
        auctions = []
        
        # Try different auction list URLs
        list_urls = [
            f"{self.base_url}/auktionen",
            f"{self.base_url}/auctions",
            f"{self.base_url}/",
        ]
        
        for url in list_urls:
            try:
                response = self.session.get(url, timeout=30)
                if response.status_code == 200:
                    page_auctions = self._extract_auctions_from_page(response.text)
                    auctions.extend(page_auctions)
                    logger.info(f"Found {len(page_auctions)} auctions from {url}")
                
                time.sleep(2)  # Rate limiting
                
            except Exception as e:
                logger.warning(f"Error parsing {url}: {e}")
                continue
        
        logger.info(f"📄 Page parsing found {len(auctions)} auctions")
        return auctions
    
    def _discover_from_categories(self) -> List[Dict]:
        """Discover auctions from category pages"""
        logger.info("🏷️ Discovering from categories...")
        
        auctions = []
        
        # Try to find category or search pages
        category_urls = [
            f"{self.base_url}/kategorien",
            f"{self.base_url}/categories", 
            f"{self.base_url}/search",
        ]
        
        for url in category_urls:
            try:
                response = self.session.get(url, timeout=30)
                if response.status_code == 200:
                    category_auctions = self._extract_auctions_from_page(response.text)
                    auctions.extend(category_auctions)
                
                time.sleep(2)
                
            except Exception as e:
                logger.debug(f"Category URL {url} not accessible: {e}")
                continue
        
        logger.info(f"🏷️ Category discovery found {len(auctions)} auctions")
        return auctions
    
    def _check_auction_exists(self, auction_id: int) -> Dict:
        """Check if an auction ID exists and get basic info"""
        try:
            url = f"{self.base_url}/auktion/{auction_id}"
            response = self.session.get(url, timeout=10)
            
            if response.status_code != 200:
                return None
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Check if it's a real auction page (not error page)
            if "nicht gefunden" in response.text.lower() or "not found" in response.text.lower():
                return None
            
            # Extract title
            title_elem = soup.find('h1') or soup.find('title')
            title = title_elem.get_text(strip=True) if title_elem else f"Auction {auction_id}"
            
            # Skip if title indicates error
            if "error" in title.lower() or "nicht gefunden" in title.lower():
                return None
            
            # Count lots
            lot_count = len(re.findall(r'/posten/\d+', response.text))
            
            # Extract location
            location = self._extract_location(response.text)
            
            # Determine status based on page content
            status = self._determine_auction_status(response.text)
            
            return {
                'auction_id': auction_id,
                'title': title,
                'start_time': datetime.now().isoformat(),
                'end_time': (datetime.now() + timedelta(days=7)).isoformat(),
                'lot_count': lot_count,
                'location': location,
                'status': status,
                'lifecycle_stage': 'discovered'
            }
            
        except Exception as e:
            logger.debug(f"Error checking auction {auction_id}: {e}")
            return None
    
    def _extract_auctions_from_page(self, page_content: str) -> List[Dict]:
        """Extract auction IDs from any page content"""
        auctions = []
        
        # Find auction links
        auction_links = re.findall(r'/auktion/(\d+)', page_content)
        
        for auction_id_str in set(auction_links):  # Remove duplicates
            try:
                auction_id = int(auction_id_str)
                
                if auction_id in self.discovered_auction_ids:
                    continue
                
                auction_data = self._check_auction_exists(auction_id)
                if auction_data:
                    auctions.append(auction_data)
                    self.discovered_auction_ids.add(auction_id)
                
                time.sleep(0.3)  # Rate limiting
                
            except (ValueError, Exception) as e:
                logger.debug(f"Error processing auction ID {auction_id_str}: {e}")
                continue
        
        return auctions
    
    def _extract_location(self, page_content: str) -> str:
        """Extract location from page content"""
        locations = ['Wien', 'Graz', 'Linz', 'Salzburg', 'Innsbruck', 'Klagenfurt', 'Bregenz']
        
        for location in locations:
            if location in page_content:
                return location
        
        return 'Austria'  # Default
    
    def _determine_auction_status(self, page_content: str) -> str:
        """Determine auction status from page content"""
        content_lower = page_content.lower()
        
        if 'beendet' in content_lower or 'closed' in content_lower:
            return 'closed'
        elif 'läuft' in content_lower or 'active' in content_lower:
            return 'active'
        else:
            return 'active'  # Default to active
    
    def _remove_duplicates(self, auctions: List[Dict]) -> List[Dict]:
        """Remove duplicate auctions"""
        seen_ids = set()
        unique_auctions = []
        
        for auction in auctions:
            auction_id = auction['auction_id']
            if auction_id not in seen_ids:
                seen_ids.add(auction_id)
                unique_auctions.append(auction)
        
        return unique_auctions
    
    def _save_discovered_auctions(self, auctions: List[Dict]):
        """Save discovered auctions to database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                current_time = datetime.now().isoformat()
                
                for auction in auctions:
                    cursor.execute("""
                        INSERT OR REPLACE INTO auctions 
                        (auction_id, title, start_time, end_time, lot_count, 
                         location, status, lifecycle_stage, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        auction['auction_id'], auction['title'], auction['start_time'],
                        auction['end_time'], auction['lot_count'], auction['location'],
                        auction['status'], auction['lifecycle_stage'], current_time, current_time
                    ))
                
                conn.commit()
                logger.info(f"💾 Saved {len(auctions)} auctions to database")
                
        except Exception as e:
            logger.error(f"Error saving auctions: {e}")

def main():
    """Run comprehensive auction discovery"""
    print("🔍 COMPREHENSIVE AUCTION DISCOVERY")
    print("=" * 60)
    print("Discovering many more real auctions from Aurena...")
    print("This will scan multiple sources to find active auctions.")
    print("=" * 60)
    
    discovery = ComprehensiveAuctionDiscovery()
    auctions = discovery.discover_all_auctions()
    
    print(f"\n🎉 DISCOVERY COMPLETE!")
    print(f"✅ Found {len(auctions)} unique auctions")
    
    if auctions:
        print(f"\n📋 SAMPLE DISCOVERED AUCTIONS:")
        for i, auction in enumerate(auctions[:10]):
            print(f"   {i+1}. Auction {auction['auction_id']}: {auction['title'][:50]}...")
            print(f"      Lots: {auction['lot_count']}, Status: {auction['status']}")
    
    print(f"\n💾 All auctions saved to database")
    print(f"🌐 View them at: http://localhost:5000")

if __name__ == "__main__":
    main()
