#!/usr/bin/env python3
"""
Lot List Scraper
Scrapes lot listings for a specific auction.
Extracts lot details including lot numbers, titles, closing times, and basic info.
"""

import requests
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from bs4 import BeautifulSoup
import json
import re
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LotListScraper:
    """Scraper for extracting lot listings from auctions"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.base_url = "https://www.aurena.at"
        
    def scrape_auction_lots(self, auction_id: int) -> List[Dict]:
        """
        Main method to scrape all lots for a specific auction.
        Returns list of lot dictionaries with basic info.
        """
        logger.info(f"🔍 Scraping lots for auction {auction_id}...")
        
        lots = []
        
        # Method 1: Extract from main auction page JavaScript
        js_lots = self._extract_lots_from_javascript(auction_id)
        lots.extend(js_lots)
        
        # Method 2: Parse HTML lot listings
        html_lots = self._extract_lots_from_html(auction_id)
        lots.extend(html_lots)
        
        # Method 3: Try API endpoints
        api_lots = self._extract_lots_from_api(auction_id)
        lots.extend(api_lots)
        
        # Remove duplicates and validate
        unique_lots = self._remove_duplicate_lots(lots)
        validated_lots = self._validate_lot_data(unique_lots, auction_id)
        
        logger.info(f"✅ Extracted {len(validated_lots)} lots from auction {auction_id}")
        return validated_lots
    
    def _extract_lots_from_javascript(self, auction_id: int) -> List[Dict]:
        """Extract lot data from embedded JavaScript in auction page"""
        try:
            url = f"{self.base_url}/auction/{auction_id}"
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            lots = []
            
            # Find scripts containing lot data
            scripts = soup.find_all('script')
            
            for script in scripts:
                if not script.string:
                    continue
                    
                script_content = script.string
                
                # Look for lot data patterns
                lot_patterns = [
                    r'lots?\s*[:=]\s*(\[.*?\])',
                    r'lotData\s*[:=]\s*(\[.*?\])',
                    r'auctionLots\s*[:=]\s*(\[.*?\])',
                    r'"lots":\s*(\[.*?\])',
                    r'"lid":\s*(\d+)',  # Individual lot ID pattern
                ]
                
                for pattern in lot_patterns:
                    matches = re.finditer(pattern, script_content, re.DOTALL | re.IGNORECASE)
                    for match in matches:
                        try:
                            if 'lid' in pattern:
                                # Handle individual lot ID pattern
                                lot_data = self._extract_single_lot_from_js(script_content, match)
                                if lot_data:
                                    lots.append(lot_data)
                            else:
                                # Handle array of lots
                                json_str = match.group(1)
                                lot_array = json.loads(json_str)
                                for lot_item in lot_array:
                                    lot_data = self._parse_js_lot_data(lot_item, auction_id)
                                    if lot_data:
                                        lots.append(lot_data)
                        except json.JSONDecodeError:
                            continue
                        except Exception as e:
                            logger.debug(f"Error parsing lot data: {e}")
                            continue
            
            logger.info(f"Found {len(lots)} lots from JavaScript")
            return lots
            
        except Exception as e:
            logger.error(f"Error extracting lots from JavaScript: {e}")
            return []
    
    def _extract_lots_from_html(self, auction_id: int) -> List[Dict]:
        """Extract lot data from HTML structure"""
        try:
            url = f"{self.base_url}/auction/{auction_id}"
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            lots = []
            
            # Look for lot containers
            lot_selectors = [
                '[class*="lot"]',
                '[id*="lot"]',
                '.auction-item',
                '.lot-item',
                'tr[data-lot]',
                'div[data-lot]'
            ]
            
            for selector in lot_selectors:
                lot_elements = soup.select(selector)
                for element in lot_elements:
                    lot_data = self._extract_lot_from_html_element(element, auction_id)
                    if lot_data:
                        lots.append(lot_data)
            
            logger.info(f"Found {len(lots)} lots from HTML")
            return lots
            
        except Exception as e:
            logger.error(f"Error extracting lots from HTML: {e}")
            return []
    
    def _extract_lots_from_api(self, auction_id: int) -> List[Dict]:
        """Extract lot data from API endpoints"""
        try:
            api_endpoints = [
                f"/api/auction/{auction_id}/lots",
                f"/api/lots?auction={auction_id}",
                f"/ajax/auction/{auction_id}/lots",
                f"/api/v1/auction/{auction_id}/lots"
            ]
            
            lots = []
            for endpoint in api_endpoints:
                try:
                    url = f"{self.base_url}{endpoint}"
                    response = self.session.get(url, timeout=15)
                    
                    if response.status_code == 200:
                        data = response.json()
                        if isinstance(data, list):
                            for item in data:
                                lot_data = self._parse_api_lot_data(item, auction_id)
                                if lot_data:
                                    lots.append(lot_data)
                        elif isinstance(data, dict) and 'lots' in data:
                            for item in data['lots']:
                                lot_data = self._parse_api_lot_data(item, auction_id)
                                if lot_data:
                                    lots.append(lot_data)
                                    
                except Exception as e:
                    logger.debug(f"API endpoint {endpoint} failed: {e}")
                    continue
            
            logger.info(f"Found {len(lots)} lots from API")
            return lots
            
        except Exception as e:
            logger.error(f"Error extracting lots from API: {e}")
            return []
    
    def _extract_single_lot_from_js(self, script_content: str, match) -> Optional[Dict]:
        """Extract single lot data from JavaScript context"""
        try:
            # Find the context around the lot ID
            start_pos = max(0, match.start() - 500)
            end_pos = min(len(script_content), match.end() + 500)
            context = script_content[start_pos:end_pos]
            
            # Extract lot ID
            lot_id_match = re.search(r'"lid":\s*(\d+)', context)
            if not lot_id_match:
                return None
            
            lot_id = int(lot_id_match.group(1))
            
            # Extract other fields from context
            title_match = re.search(r'"title":\s*"([^"]*)"', context)
            title = title_match.group(1) if title_match else f"Lot {lot_id}"
            
            price_match = re.search(r'"price":\s*(\d+)', context)
            current_price = int(price_match.group(1)) if price_match else 0
            
            closing_match = re.search(r'"closing":\s*(\d+)', context)
            closing_time = None
            if closing_match:
                closing_timestamp = int(closing_match.group(1))
                closing_time = datetime.fromtimestamp(closing_timestamp / 1000)
            
            return {
                'lot_id': lot_id,
                'lot_number': lot_id,  # May be different in some cases
                'title': title,
                'current_price': current_price,
                'closing_time': closing_time,
                'status': 'active'
            }
            
        except Exception as e:
            logger.debug(f"Error extracting single lot from JS: {e}")
            return None
    
    def _parse_js_lot_data(self, lot_item: Dict, auction_id: int) -> Optional[Dict]:
        """Parse lot data from JavaScript object"""
        try:
            # Common field mappings
            id_fields = ['lid', 'lot_id', 'id', 'lotId']
            title_fields = ['title', 'name', 'description', 'subject']
            price_fields = ['price', 'current_price', 'currentPrice', 'amount']
            closing_fields = ['closing', 'closing_time', 'closingTime', 'end_time']
            
            # Extract lot ID
            lot_id = None
            for field in id_fields:
                if field in lot_item and lot_item[field]:
                    lot_id = int(lot_item[field])
                    break
            
            if not lot_id:
                return None
            
            # Extract title
            title = f"Lot {lot_id}"
            for field in title_fields:
                if field in lot_item and lot_item[field]:
                    title = str(lot_item[field])
                    break
            
            # Extract price
            current_price = 0
            for field in price_fields:
                if field in lot_item and lot_item[field]:
                    current_price = int(lot_item[field])
                    break
            
            # Extract closing time
            closing_time = None
            for field in closing_fields:
                if field in lot_item and lot_item[field]:
                    timestamp = lot_item[field]
                    if isinstance(timestamp, (int, float)):
                        closing_time = datetime.fromtimestamp(timestamp / 1000)
                    break
            
            return {
                'lot_id': lot_id,
                'auction_id': auction_id,
                'lot_number': lot_item.get('lot_number', lot_id),
                'title': title,
                'current_price': current_price,
                'closing_time': closing_time,
                'status': 'active',
                'description': lot_item.get('description', ''),
                'starting_price': lot_item.get('starting_price', 0)
            }
            
        except Exception as e:
            logger.debug(f"Error parsing JS lot data: {e}")
            return None
    
    def _extract_lot_from_html_element(self, element, auction_id: int) -> Optional[Dict]:
        """Extract lot data from HTML element"""
        try:
            # Extract lot ID
            lot_id = None
            id_patterns = [
                r'lot[_-]?(\d+)',
                r'data-lot["\']?[=:]?["\']?(\d+)',
                r'id["\']?[=:]?["\']?lot[_-]?(\d+)'
            ]
            
            element_text = str(element)
            for pattern in id_patterns:
                match = re.search(pattern, element_text, re.IGNORECASE)
                if match:
                    lot_id = int(match.group(1))
                    break
            
            if not lot_id:
                return None
            
            # Extract title
            title_elem = element.find(['h1', 'h2', 'h3', 'h4', 'span'], class_=re.compile(r'title|name'))
            if not title_elem:
                title_elem = element.find(text=re.compile(r'lot', re.IGNORECASE))
            title = title_elem.get_text(strip=True) if title_elem else f"Lot {lot_id}"
            
            # Extract price
            price_elem = element.find(text=re.compile(r'€\s*\d+|\d+\s*€'))
            current_price = 0
            if price_elem:
                price_match = re.search(r'(\d+)', price_elem)
                if price_match:
                    current_price = int(price_match.group(1))
            
            return {
                'lot_id': lot_id,
                'auction_id': auction_id,
                'lot_number': lot_id,
                'title': title,
                'current_price': current_price,
                'closing_time': None,  # Would need more specific parsing
                'status': 'active'
            }
            
        except Exception as e:
            logger.debug(f"Error extracting lot from HTML element: {e}")
            return None
    
    def _parse_api_lot_data(self, lot_item: Dict, auction_id: int) -> Optional[Dict]:
        """Parse lot data from API response"""
        # Similar to _parse_js_lot_data but for API responses
        return self._parse_js_lot_data(lot_item, auction_id)
    
    def _remove_duplicate_lots(self, lots: List[Dict]) -> List[Dict]:
        """Remove duplicate lots based on lot_id"""
        seen_ids = set()
        unique_lots = []
        
        for lot in lots:
            lot_id = lot.get('lot_id')
            if lot_id and lot_id not in seen_ids:
                seen_ids.add(lot_id)
                unique_lots.append(lot)
        
        return unique_lots
    
    def _validate_lot_data(self, lots: List[Dict], auction_id: int) -> List[Dict]:
        """Validate and clean lot data"""
        validated_lots = []
        
        for lot in lots:
            # Ensure required fields
            if not lot.get('lot_id'):
                continue
            
            # Set auction_id if missing
            if not lot.get('auction_id'):
                lot['auction_id'] = auction_id
            
            # Set default values
            lot.setdefault('status', 'active')
            lot.setdefault('current_price', 0)
            lot.setdefault('starting_price', 0)
            lot.setdefault('description', '')
            
            # Ensure title exists
            if not lot.get('title'):
                lot['title'] = f"Lot {lot['lot_id']}"
            
            validated_lots.append(lot)
        
        return validated_lots


if __name__ == "__main__":
    scraper = LotListScraper()
    
    # Test with a specific auction ID (would be provided dynamically)
    test_auction_id = 14941  # This is just for testing - normally would come from auction discovery
    lots = scraper.scrape_auction_lots(test_auction_id)
    
    print(f"\n=== EXTRACTED LOTS ===")
    for lot in lots[:5]:  # Show first 5
        print(f"Lot {lot['lot_id']}: {lot['title']} - €{lot['current_price']}")
