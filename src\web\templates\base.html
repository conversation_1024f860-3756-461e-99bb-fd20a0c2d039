<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Auction Scraper Dashboard{% endblock %}</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .main-content {
            min-height: 100vh;
        }
        .status-badge {
            font-size: 0.75rem;
        }
        .auction-tree {
            max-height: 400px;
            overflow-y: auto;
        }
        .lot-card {
            transition: transform 0.2s;
        }
        .lot-card:hover {
            transform: translateY(-2px);
        }
        .closing-soon {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .stats-card {
            border-left: 4px solid #007bff;
        }
        .refresh-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-hammer"></i> Auction Scraper
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="bi bi-house"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auctions_list') }}">
                            <i class="bi bi-list"></i> Auctions
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('timetable') }}">
                            <i class="bi bi-calendar"></i> Timetable
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auction_tree') }}">
                            <i class="bi bi-diagram-3"></i> Tree View
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('scraping_control') }}">
                            <i class="bi bi-gear"></i> Control Panel
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text">
                            <i class="bi bi-clock"></i> <span id="current-time"></span>
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Refresh Indicator -->
    <div id="refresh-indicator" class="refresh-indicator" style="display: none;">
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <i class="bi bi-arrow-clockwise"></i> Refreshing data...
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleTimeString();
        }
        
        // Update time every second
        setInterval(updateTime, 1000);
        updateTime();
        
        // Auto-refresh functionality
        let autoRefreshInterval;
        
        function startAutoRefresh(intervalSeconds = 30) {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
            
            autoRefreshInterval = setInterval(() => {
                showRefreshIndicator();
                location.reload();
            }, intervalSeconds * 1000);
        }
        
        function showRefreshIndicator() {
            const indicator = document.getElementById('refresh-indicator');
            indicator.style.display = 'block';
            setTimeout(() => {
                indicator.style.display = 'none';
            }, 2000);
        }
        
        // Start auto-refresh on dashboard and timetable pages
        if (window.location.pathname === '/' || window.location.pathname.includes('timetable')) {
            startAutoRefresh(30);
        }
        
        // API helper functions
        async function apiCall(endpoint, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(endpoint, options);
                return await response.json();
            } catch (error) {
                console.error('API call failed:', error);
                return { success: false, message: error.message };
            }
        }
        
        // Manual trigger functions
        async function triggerAuctionDiscovery() {
            const button = event.target;
            const originalText = button.textContent;
            
            button.disabled = true;
            button.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Discovering...';
            
            try {
                const result = await apiCall('/api/discover-auctions', 'POST');
                
                if (result.success) {
                    showAlert('success', 'Auction discovery triggered successfully!');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showAlert('danger', 'Failed to trigger auction discovery: ' + result.message);
                }
            } catch (error) {
                showAlert('danger', 'Error: ' + error.message);
            } finally {
                button.disabled = false;
                button.innerHTML = originalText;
            }
        }
        
        async function triggerLotDiscovery(auctionId) {
            const button = event.target;
            const originalText = button.textContent;
            
            button.disabled = true;
            button.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Discovering...';
            
            try {
                const result = await apiCall(`/api/discover-lots/${auctionId}`, 'POST');
                
                if (result.success) {
                    showAlert('success', 'Lot discovery triggered successfully!');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showAlert('danger', 'Failed to trigger lot discovery: ' + result.message);
                }
            } catch (error) {
                showAlert('danger', 'Error: ' + error.message);
            } finally {
                button.disabled = false;
                button.innerHTML = originalText;
            }
        }
        
        async function triggerBidCollection(lotId) {
            const button = event.target;
            const originalText = button.textContent;
            
            button.disabled = true;
            button.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Collecting...';
            
            try {
                const result = await apiCall(`/api/collect-bids/${lotId}`, 'POST');
                
                if (result.success) {
                    showAlert('success', 'Bid collection triggered successfully!');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showAlert('danger', 'Failed to trigger bid collection: ' + result.message);
                }
            } catch (error) {
                showAlert('danger', 'Error: ' + error.message);
            } finally {
                button.disabled = false;
                button.innerHTML = originalText;
            }
        }
        
        function showAlert(type, message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            // Insert at top of main content
            const container = document.querySelector('.container-fluid');
            container.insertBefore(alertDiv, container.firstChild);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
        
        // Add spinning animation for refresh icons
        const style = document.createElement('style');
        style.textContent = `
            .spin {
                animation: spin 1s linear infinite;
            }
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
