#!/usr/bin/env python3
"""
Real-time Auction Monitor for Aurena Auctions
Monitors lots before and after closing times to ensure accurate bid tracking.
"""

import time
import threading
from datetime import datetime, timedelta
from typing import List, Dict
import logging
import sqlite3
from bid_fetcher import BidFetcher
from auction_crawler import AuctionCrawler

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealtimeMonitor:
    """Real-time monitoring system for auction lots"""
    
    def __init__(self, db_path: str = "aurena_auctions.db"):
        self.db_path = db_path
        self.bid_fetcher = BidFetcher(db_path)
        self.crawler = AuctionCrawler(db_path)
        self.running = False
        self.monitor_thread = None
        
        # Monitoring configuration
        self.check_interval = 30  # Check every 30 seconds
        self.critical_window = 300  # 5 minutes before closing
        self.post_close_window = 300  # 5 minutes after closing
        self.pre_monitor_window = 7200  # 2 hours before closing
        
    def start_monitoring(self):
        """Start the real-time monitoring system"""
        if self.running:
            logger.warning("Monitor is already running")
            return
        
        self.running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("Real-time monitor started")
    
    def stop_monitoring(self):
        """Stop the real-time monitoring system"""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("Real-time monitor stopped")
    
    def _monitor_loop(self):
        """Main monitoring loop"""
        logger.info("Starting monitoring loop...")
        
        while self.running:
            try:
                # Get lots that need monitoring
                lots_to_monitor = self._get_lots_for_monitoring()
                
                if lots_to_monitor:
                    logger.info(f"Monitoring {len(lots_to_monitor)} lots")
                    
                    for lot in lots_to_monitor:
                        if not self.running:
                            break
                        
                        self._monitor_lot(lot)
                        
                        # Small delay between lots to avoid overwhelming the server
                        time.sleep(2)
                else:
                    logger.info("No lots currently need monitoring")
                
                # Wait before next check
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(60)  # Wait longer on error
    
    def _get_lots_for_monitoring(self) -> List[Dict]:
        """Get lots that need monitoring based on closing times"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                now = datetime.now()
                
                # Monitor lots from 2 hours before to 5 minutes after closing
                monitor_start = (now - timedelta(seconds=self.post_close_window)).isoformat()
                monitor_end = (now + timedelta(seconds=self.pre_monitor_window)).isoformat()
                
                cursor.execute("""
                    SELECT l.lot_id, l.auction_id, l.title, l.closing_time, 
                           l.current_price, l.status
                    FROM lots l
                    WHERE l.closing_time BETWEEN ? AND ?
                    AND l.status = 'active'
                    ORDER BY l.closing_time
                """, (monitor_start, monitor_end))
                
                lots = []
                for row in cursor.fetchall():
                    lot = dict(row)
                    lot['closing_time'] = datetime.fromisoformat(lot['closing_time'])
                    
                    # Calculate time to closing
                    time_to_close = (lot['closing_time'] - now).total_seconds()
                    lot['time_to_close'] = time_to_close
                    
                    # Determine monitoring priority
                    if time_to_close < 0:
                        # Already closed - check for extensions
                        if abs(time_to_close) < self.post_close_window:
                            lot['priority'] = 'post_close'
                        else:
                            continue  # Too old, skip
                    elif time_to_close < self.critical_window:
                        # Critical - closing soon
                        lot['priority'] = 'critical'
                    elif time_to_close < 3600:  # 1 hour
                        # High priority
                        lot['priority'] = 'high'
                    else:
                        # Normal monitoring
                        lot['priority'] = 'normal'
                    
                    lots.append(lot)
                
                return lots
                
        except Exception as e:
            logger.error(f"Error getting lots for monitoring: {e}")
            return []
    
    def _monitor_lot(self, lot: Dict):
        """Monitor a specific lot"""
        lot_id = lot['lot_id']
        auction_id = lot['auction_id']
        title = lot['title']
        closing_time = lot['closing_time']
        priority = lot['priority']
        
        logger.info(f"Monitoring lot {lot_id} ({priority} priority) - closes at {closing_time}")
        
        try:
            # Fetch current bid history
            bids = self.bid_fetcher.fetch_lot_bid_history(lot_id, title)
            
            if bids:
                # Save bid history
                self.bid_fetcher.save_bid_history(lot_id, auction_id, bids)
                
                # Update lot's current price if we have newer data
                latest_bid = bids[0]  # Bids are sorted newest first
                new_price = latest_bid['amount']
                
                if new_price != lot['current_price']:
                    self._update_lot_price(lot_id, new_price)
                    logger.info(f"Updated lot {lot_id} price: €{lot['current_price']//100} -> €{new_price//100}")
                
                # Check for lot extensions (if closing time has changed)
                self._check_lot_extension(lot_id, closing_time)
                
                # Log monitoring activity
                self._log_monitoring_activity(lot_id, len(bids), priority)
                
            else:
                logger.warning(f"No bids found for lot {lot_id}")
                
        except Exception as e:
            logger.error(f"Error monitoring lot {lot_id}: {e}")
    
    def _update_lot_price(self, lot_id: int, new_price: int):
        """Update lot's current price in database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE lots SET current_price = ?, updated_at = ?
                    WHERE lot_id = ?
                """, (new_price, datetime.now().isoformat(), lot_id))
                conn.commit()
        except Exception as e:
            logger.error(f"Error updating lot price: {e}")
    
    def _check_lot_extension(self, lot_id: int, expected_closing: datetime):
        """Check if lot closing time has been extended"""
        try:
            # Re-fetch lot data from the main crawler to check for extensions
            # This would require enhancing the crawler to detect time changes
            pass
        except Exception as e:
            logger.error(f"Error checking lot extension: {e}")
    
    def _log_monitoring_activity(self, lot_id: int, bid_count: int, priority: str):
        """Log monitoring activity to database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Create monitoring log table if it doesn't exist
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS monitoring_log (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        lot_id INTEGER NOT NULL,
                        timestamp TEXT NOT NULL,
                        bid_count INTEGER NOT NULL,
                        priority TEXT NOT NULL,
                        FOREIGN KEY (lot_id) REFERENCES lots (lot_id)
                    )
                """)
                
                cursor.execute("""
                    INSERT INTO monitoring_log (lot_id, timestamp, bid_count, priority)
                    VALUES (?, ?, ?, ?)
                """, (lot_id, datetime.now().isoformat(), bid_count, priority))
                
                conn.commit()
        except Exception as e:
            logger.error(f"Error logging monitoring activity: {e}")
    
    def get_monitoring_stats(self) -> Dict:
        """Get monitoring statistics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get current monitoring status
                now = datetime.now()
                monitor_start = (now - timedelta(seconds=self.post_close_window)).isoformat()
                monitor_end = (now + timedelta(seconds=self.pre_monitor_window)).isoformat()
                
                cursor.execute("""
                    SELECT COUNT(*) FROM lots 
                    WHERE closing_time BETWEEN ? AND ? AND status = 'active'
                """, (monitor_start, monitor_end))
                lots_being_monitored = cursor.fetchone()[0]
                
                # Get critical lots (closing in next 5 minutes)
                critical_end = (now + timedelta(seconds=self.critical_window)).isoformat()
                cursor.execute("""
                    SELECT COUNT(*) FROM lots 
                    WHERE closing_time BETWEEN ? AND ? AND status = 'active'
                """, (now.isoformat(), critical_end))
                critical_lots = cursor.fetchone()[0]
                
                # Get recent monitoring activity
                recent_time = (now - timedelta(minutes=10)).isoformat()
                cursor.execute("""
                    SELECT COUNT(*) FROM monitoring_log 
                    WHERE timestamp > ?
                """, (recent_time,))
                recent_checks = cursor.fetchone()[0]
                
                return {
                    'lots_being_monitored': lots_being_monitored,
                    'critical_lots': critical_lots,
                    'recent_checks': recent_checks,
                    'monitor_running': self.running
                }
                
        except Exception as e:
            logger.error(f"Error getting monitoring stats: {e}")
            return {}

def main():
    """Main function for testing"""
    monitor = RealtimeMonitor()
    
    print("🔄 Starting Real-time Auction Monitor")
    print("⏰ Monitoring lots 2 hours before to 5 minutes after closing")
    print("🎯 Critical monitoring: 5 minutes before closing")
    print("📊 Check interval: 30 seconds")
    
    try:
        # Start monitoring
        monitor.start_monitoring()
        
        # Run for a test period
        for i in range(10):  # Run for 5 minutes (10 * 30 seconds)
            time.sleep(30)
            
            # Show stats
            stats = monitor.get_monitoring_stats()
            print(f"\n📊 Monitoring Stats (check {i+1}):")
            print(f"   Lots being monitored: {stats.get('lots_being_monitored', 0)}")
            print(f"   Critical lots: {stats.get('critical_lots', 0)}")
            print(f"   Recent checks: {stats.get('recent_checks', 0)}")
            print(f"   Monitor running: {stats.get('monitor_running', False)}")
        
    except KeyboardInterrupt:
        print("\n🛑 Stopping monitor...")
    finally:
        monitor.stop_monitoring()
        print("✅ Monitor stopped")

if __name__ == "__main__":
    main()
