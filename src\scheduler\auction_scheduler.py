#!/usr/bin/env python3
"""
Auction Scheduler
Implements timing and scheduling logic according to requirements:
- Low-frequency auction list updates
- High-frequency bid tracking near closing times
- Precise timing for bid collection (5 seconds before/after closing)
"""

import threading
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum

from ..database.manager import DatabaseManager
from ..database.models import Auction, Lot, Bid
from ..scraper.auction_list_scraper import AuctionListScraper
from ..scraper.lot_list_scraper import LotListScraper
from ..scraper.lot_details_scraper import LotDetailsScraper

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ScheduleType(Enum):
    """Types of scheduled operations"""
    AUCTION_DISCOVERY = "auction_discovery"
    LOT_DISCOVERY = "lot_discovery"
    BID_COLLECTION = "bid_collection"
    CLOSING_MONITOR = "closing_monitor"


@dataclass
class ScheduledTask:
    """Represents a scheduled task"""
    task_id: str
    task_type: ScheduleType
    target_id: Optional[int]  # auction_id or lot_id
    scheduled_time: datetime
    callback: Callable
    priority: int = 1
    max_retries: int = 3
    retry_count: int = 0
    status: str = "pending"  # pending, running, completed, failed


class AuctionScheduler:
    """Main scheduler for auction scraping operations"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.auction_scraper = AuctionListScraper()
        self.lot_scraper = LotListScraper()
        self.details_scraper = LotDetailsScraper()
        
        # Scheduling configuration
        self.config = {
            'auction_discovery_interval': 3600,  # 1 hour - low frequency
            'lot_discovery_trigger': 7200,       # 2 hours before auction start
            'bid_monitor_window': 300,           # 5 minutes before closing
            'critical_window': 10,               # 10 seconds for critical timing
            'post_close_monitor': 300,           # 5 minutes after closing
            'extension_check_interval': 60       # Check for 60-second extensions
        }
        
        # Task management
        self.scheduled_tasks: Dict[str, ScheduledTask] = {}
        self.running = False
        self.scheduler_thread: Optional[threading.Thread] = None
        self.task_lock = threading.Lock()
        
    def start(self):
        """Start the scheduler"""
        if self.running:
            logger.warning("Scheduler already running")
            return
        
        self.running = True
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        
        # Schedule initial auction discovery
        self._schedule_auction_discovery()
        
        logger.info("🚀 Auction scheduler started")
    
    def stop(self):
        """Stop the scheduler"""
        self.running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        logger.info("⏹️ Auction scheduler stopped")
    
    def _scheduler_loop(self):
        """Main scheduler loop"""
        while self.running:
            try:
                current_time = datetime.now()
                
                # Process due tasks
                due_tasks = []
                with self.task_lock:
                    for task_id, task in self.scheduled_tasks.items():
                        if (task.status == "pending" and 
                            task.scheduled_time <= current_time):
                            due_tasks.append(task)
                
                # Execute due tasks
                for task in due_tasks:
                    self._execute_task(task)
                
                # Clean up completed tasks
                self._cleanup_completed_tasks()
                
                # Sleep for 1 second
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"Error in scheduler loop: {e}")
                time.sleep(5)
    
    def _execute_task(self, task: ScheduledTask):
        """Execute a scheduled task"""
        try:
            with self.task_lock:
                task.status = "running"
            
            logger.info(f"🔄 Executing task {task.task_id} ({task.task_type.value})")
            
            # Execute the task callback
            success = task.callback()
            
            with self.task_lock:
                if success:
                    task.status = "completed"
                    logger.info(f"✅ Task {task.task_id} completed successfully")
                else:
                    task.retry_count += 1
                    if task.retry_count < task.max_retries:
                        task.status = "pending"
                        task.scheduled_time = datetime.now() + timedelta(seconds=30)
                        logger.warning(f"⚠️ Task {task.task_id} failed, retrying in 30s")
                    else:
                        task.status = "failed"
                        logger.error(f"❌ Task {task.task_id} failed after {task.max_retries} retries")
        
        except Exception as e:
            logger.error(f"Error executing task {task.task_id}: {e}")
            with self.task_lock:
                task.status = "failed"
    
    def _cleanup_completed_tasks(self):
        """Remove completed and old failed tasks"""
        cutoff_time = datetime.now() - timedelta(hours=1)
        
        with self.task_lock:
            to_remove = []
            for task_id, task in self.scheduled_tasks.items():
                if (task.status in ["completed", "failed"] and 
                    task.scheduled_time < cutoff_time):
                    to_remove.append(task_id)
            
            for task_id in to_remove:
                del self.scheduled_tasks[task_id]
    
    def _schedule_auction_discovery(self):
        """Schedule periodic auction discovery"""
        def discover_auctions():
            try:
                logger.info("🔍 Starting scheduled auction discovery...")
                auctions_data = self.auction_scraper.discover_auctions()
                
                if auctions_data:
                    # Convert to Auction objects
                    auctions = []
                    for data in auctions_data:
                        auction = Auction(
                            auction_id=data['auction_id'],
                            title=data['title'],
                            start_time=data.get('start_time'),
                            status=data.get('status', 'active'),
                            lifecycle_stage='discovered'
                        )
                        auctions.append(auction)
                    
                    # Save to database
                    success = self.db_manager.save_auctions(auctions)
                    
                    if success:
                        # Schedule lot discovery for new auctions
                        self._schedule_lot_discovery_for_auctions(auctions)
                        
                        # Log the action
                        self.db_manager.log_lifecycle_action(
                            None, None, 'auction_discovery', 'success',
                            f"Discovered {len(auctions)} auctions"
                        )
                    
                    return success
                
                return False
                
            except Exception as e:
                logger.error(f"Error in auction discovery: {e}")
                return False
        
        # Schedule next discovery
        next_discovery = datetime.now() + timedelta(seconds=self.config['auction_discovery_interval'])
        
        task = ScheduledTask(
            task_id=f"auction_discovery_{int(time.time())}",
            task_type=ScheduleType.AUCTION_DISCOVERY,
            target_id=None,
            scheduled_time=next_discovery,
            callback=discover_auctions,
            priority=2
        )
        
        with self.task_lock:
            self.scheduled_tasks[task.task_id] = task
        
        # Schedule the next discovery
        def schedule_next():
            time.sleep(1)  # Small delay to avoid immediate rescheduling
            if self.running:
                self._schedule_auction_discovery()
        
        threading.Thread(target=schedule_next, daemon=True).start()
    
    def _schedule_lot_discovery_for_auctions(self, auctions: List[Auction]):
        """Schedule lot discovery for auctions"""
        for auction in auctions:
            if auction.start_time:
                # Schedule lot discovery 2 hours before auction start
                discovery_time = auction.start_time - timedelta(seconds=self.config['lot_discovery_trigger'])
                
                # If discovery time is in the past, schedule immediately
                if discovery_time < datetime.now():
                    discovery_time = datetime.now() + timedelta(seconds=10)
                
                def discover_lots(auction_id=auction.auction_id):
                    try:
                        logger.info(f"🔍 Discovering lots for auction {auction_id}")
                        lots_data = self.lot_scraper.scrape_auction_lots(auction_id)
                        
                        if lots_data:
                            # Convert to Lot objects
                            lots = []
                            for data in lots_data:
                                lot = Lot(
                                    lot_id=data['lot_id'],
                                    auction_id=data['auction_id'],
                                    lot_number=data.get('lot_number', data['lot_id']),
                                    title=data.get('title', f"Lot {data['lot_id']}"),
                                    current_price=data.get('current_price', 0),
                                    closing_time=data.get('closing_time'),
                                    status=data.get('status', 'active'),
                                    lifecycle_stage='lots_discovered'
                                )
                                lots.append(lot)
                            
                            # Save to database
                            success = self.db_manager.save_lots(lots)
                            
                            if success:
                                # Schedule bid monitoring for lots with closing times
                                self._schedule_bid_monitoring_for_lots(lots)
                                
                                # Update auction lot count
                                auction_obj = self.db_manager.get_auction(auction_id)
                                if auction_obj:
                                    auction_obj.lot_count = len(lots)
                                    auction_obj.lifecycle_stage = 'lots_discovered'
                                    self.db_manager.save_auction(auction_obj)
                                
                                # Log the action
                                self.db_manager.log_lifecycle_action(
                                    auction_id, None, 'lot_discovery', 'success',
                                    f"Discovered {len(lots)} lots"
                                )
                            
                            return success
                        
                        return False
                        
                    except Exception as e:
                        logger.error(f"Error discovering lots for auction {auction_id}: {e}")
                        return False
                
                task = ScheduledTask(
                    task_id=f"lot_discovery_{auction.auction_id}_{int(time.time())}",
                    task_type=ScheduleType.LOT_DISCOVERY,
                    target_id=auction.auction_id,
                    scheduled_time=discovery_time,
                    callback=discover_lots,
                    priority=3
                )
                
                with self.task_lock:
                    self.scheduled_tasks[task.task_id] = task
    
    def _schedule_bid_monitoring_for_lots(self, lots: List[Lot]):
        """Schedule bid monitoring for lots with closing times"""
        for lot in lots:
            if lot.closing_time:
                # Schedule bid collection 5 minutes before closing
                monitor_time = lot.closing_time - timedelta(seconds=self.config['bid_monitor_window'])
                
                # If monitor time is in the past, schedule immediately
                if monitor_time < datetime.now():
                    monitor_time = datetime.now() + timedelta(seconds=5)
                
                def monitor_bids(lot_id=lot.lot_id, closing_time=lot.closing_time):
                    try:
                        logger.info(f"📊 Starting bid monitoring for lot {lot_id}")
                        
                        # Use timed bid collection
                        bids_data = self.details_scraper.scrape_lot_bids_timed(lot_id, closing_time)
                        
                        if bids_data:
                            # Convert to Bid objects
                            bids = []
                            for data in bids_data:
                                bid = Bid(
                                    lot_id=data['lot_id'],
                                    auction_id=lot.auction_id,
                                    amount=data['amount'],
                                    timestamp=data['timestamp'],
                                    bid_date=data['bid_date'],
                                    bid_time=data['bid_time']
                                )
                                bids.append(bid)
                            
                            # Save to database
                            success = self.db_manager.save_bids(bids)
                            
                            if success:
                                # Update lot with bid info
                                lot_obj = self.db_manager.get_lot(lot_id)
                                if lot_obj:
                                    lot_obj.bid_count = len(bids)
                                    lot_obj.bids_fetched = True
                                    lot_obj.lifecycle_stage = 'bids_collected'
                                    if bids:
                                        lot_obj.current_price = max(bid.amount for bid in bids)
                                    self.db_manager.save_lot(lot_obj)
                                
                                # Schedule post-closing check
                                self._schedule_post_closing_check(lot_id, closing_time)
                                
                                # Log the action
                                self.db_manager.log_lifecycle_action(
                                    lot.auction_id, lot_id, 'bid_collection', 'success',
                                    f"Collected {len(bids)} bids"
                                )
                            
                            return success
                        
                        return True  # No bids is not necessarily a failure
                        
                    except Exception as e:
                        logger.error(f"Error monitoring bids for lot {lot_id}: {e}")
                        return False
                
                task = ScheduledTask(
                    task_id=f"bid_monitor_{lot.lot_id}_{int(time.time())}",
                    task_type=ScheduleType.BID_COLLECTION,
                    target_id=lot.lot_id,
                    scheduled_time=monitor_time,
                    callback=monitor_bids,
                    priority=1  # High priority for bid collection
                )
                
                with self.task_lock:
                    self.scheduled_tasks[task.task_id] = task
    
    def _schedule_post_closing_check(self, lot_id: int, closing_time: datetime):
        """Schedule post-closing check for extensions"""
        check_time = closing_time + timedelta(seconds=self.config['post_close_monitor'])
        
        def check_extensions(lot_id=lot_id):
            try:
                logger.info(f"🔍 Checking for extensions on lot {lot_id}")
                
                # Get fresh lot details to check for extensions
                lot_details = self.details_scraper.scrape_lot_details(lot_id, include_bids=True)
                
                if lot_details:
                    # Check if lot is still active (indicating possible extension)
                    if lot_details.get('status') == 'active':
                        logger.info(f"⏰ Lot {lot_id} may have been extended")
                        
                        # Update lot with new information
                        lot_obj = self.db_manager.get_lot(lot_id)
                        if lot_obj:
                            lot_obj.status = lot_details.get('status', 'active')
                            if lot_details.get('closing_time'):
                                lot_obj.closing_time = lot_details['closing_time']
                            lot_obj.lifecycle_stage = 'extension_detected'
                            self.db_manager.save_lot(lot_obj)
                            
                            # If extended, schedule new bid monitoring
                            if lot_details.get('closing_time') and lot_details['closing_time'] > datetime.now():
                                self._schedule_bid_monitoring_for_lots([lot_obj])
                    else:
                        # Lot is closed, update status
                        lot_obj = self.db_manager.get_lot(lot_id)
                        if lot_obj:
                            lot_obj.status = 'closed'
                            lot_obj.lifecycle_stage = 'completed'
                            self.db_manager.save_lot(lot_obj)
                    
                    # Log the action
                    self.db_manager.log_lifecycle_action(
                        None, lot_id, 'closing_check', 'completed',
                        f"Status: {lot_details.get('status', 'unknown')}"
                    )
                
                return True
                
            except Exception as e:
                logger.error(f"Error checking extensions for lot {lot_id}: {e}")
                return False
        
        task = ScheduledTask(
            task_id=f"closing_check_{lot_id}_{int(time.time())}",
            task_type=ScheduleType.CLOSING_MONITOR,
            target_id=lot_id,
            scheduled_time=check_time,
            callback=check_extensions,
            priority=2
        )
        
        with self.task_lock:
            self.scheduled_tasks[task.task_id] = task
    
    def get_scheduled_tasks(self) -> List[Dict]:
        """Get list of scheduled tasks for monitoring"""
        with self.task_lock:
            tasks = []
            for task in self.scheduled_tasks.values():
                tasks.append({
                    'task_id': task.task_id,
                    'task_type': task.task_type.value,
                    'target_id': task.target_id,
                    'scheduled_time': task.scheduled_time.isoformat(),
                    'status': task.status,
                    'priority': task.priority,
                    'retry_count': task.retry_count
                })
            return sorted(tasks, key=lambda x: x['scheduled_time'])
    
    def trigger_manual_auction_discovery(self) -> bool:
        """Manually trigger auction discovery"""
        def discover_now():
            return self.auction_scraper.discover_auctions() is not None
        
        task = ScheduledTask(
            task_id=f"manual_auction_discovery_{int(time.time())}",
            task_type=ScheduleType.AUCTION_DISCOVERY,
            target_id=None,
            scheduled_time=datetime.now(),
            callback=discover_now,
            priority=1
        )
        
        with self.task_lock:
            self.scheduled_tasks[task.task_id] = task
        
        return True
    
    def trigger_manual_lot_discovery(self, auction_id: int) -> bool:
        """Manually trigger lot discovery for an auction"""
        def discover_lots():
            lots_data = self.lot_scraper.scrape_auction_lots(auction_id)
            return lots_data is not None
        
        task = ScheduledTask(
            task_id=f"manual_lot_discovery_{auction_id}_{int(time.time())}",
            task_type=ScheduleType.LOT_DISCOVERY,
            target_id=auction_id,
            scheduled_time=datetime.now(),
            callback=discover_lots,
            priority=1
        )
        
        with self.task_lock:
            self.scheduled_tasks[task.task_id] = task
        
        return True
    
    def trigger_manual_bid_collection(self, lot_id: int) -> bool:
        """Manually trigger bid collection for a lot"""
        def collect_bids():
            lot = self.db_manager.get_lot(lot_id)
            if lot and lot.closing_time:
                bids_data = self.details_scraper.scrape_lot_bids_timed(lot_id, lot.closing_time)
                return bids_data is not None
            else:
                # Collect current bids without timing
                lot_details = self.details_scraper.scrape_lot_details(lot_id, include_bids=True)
                return lot_details is not None
        
        task = ScheduledTask(
            task_id=f"manual_bid_collection_{lot_id}_{int(time.time())}",
            task_type=ScheduleType.BID_COLLECTION,
            target_id=lot_id,
            scheduled_time=datetime.now(),
            callback=collect_bids,
            priority=1
        )
        
        with self.task_lock:
            self.scheduled_tasks[task.task_id] = task
        
        return True
