<!DOCTYPE html>
<html>
<head>
    <title>Auction Timetable - <PERSON>rena Tracker</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .timetable-row {
            border-left: 4px solid #dee2e6;
            transition: all 0.3s ease;
        }
        .timetable-row:hover {
            background-color: #f8f9fa;
            transform: translateX(5px);
        }
        .status-waiting { border-left-color: #6c757d; }
        .status-active { border-left-color: #198754; }
        .status-closing { border-left-color: #fd7e14; }
        .status-critical { border-left-color: #dc3545; }
        .status-closed { border-left-color: #6c757d; }
        .countdown {
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-gavel me-2"></i>
                Aurena Auction Tracker
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Dashboard</a>
                <a class="nav-link active" href="/timetable">Timetable</a>
                <a class="nav-link" href="/auction-tree">Auction Tree</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-clock me-2"></i>Auction Timetable</h2>
            <div class="btn-group" role="group">
                <a href="/timetable?hours=24" class="btn btn-outline-primary {{ 'active' if hours == 24 else '' }}">24h</a>
                <a href="/timetable?hours=48" class="btn btn-outline-primary {{ 'active' if hours == 48 else '' }}">48h</a>
                <a href="/timetable?hours=168" class="btn btn-outline-primary {{ 'active' if hours == 168 else '' }}">7 days</a>
            </div>
        </div>

        <!-- Status Legend -->
        <div class="card mb-4">
            <div class="card-body">
                <h6>Status Legend:</h6>
                <div class="d-flex flex-wrap gap-3">
                    <span><span class="badge bg-info">Waiting</span> - More than 1 hour to close</span>
                    <span><span class="badge bg-warning">Closing Soon</span> - 5 minutes to 1 hour</span>
                    <span><span class="badge bg-danger">Critical</span> - Less than 5 minutes</span>
                    <span><span class="badge bg-secondary">Closed</span> - Auction ended</span>
                </div>
            </div>
        </div>

        <!-- Timetable Data -->
        <div class="card">
            <div class="card-header">
                <h5>Lots Closing in Next {{ hours }} Hours ({{ timetable_data|length }} lots)</h5>
            </div>
            <div class="card-body">
                {% if timetable_data %}
                    {% for lot in timetable_data %}
                    <div class="timetable-row p-3 mb-3 border rounded status-{{ lot.status_class }}">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="mb-1">
                                    <a href="/lot/{{ lot.lot_id }}" class="text-decoration-none">
                                        <i class="fas fa-box me-1"></i>
                                        Lot {{ lot.lot_number }}: {{ lot.title }}
                                    </a>
                                </h6>
                                <p class="text-muted small mb-1">
                                    <i class="fas fa-gavel me-1"></i>
                                    <a href="/auction/{{ lot.auction_id }}" class="text-decoration-none">
                                        Auction {{ lot.auction_id }}: {{ lot.auction_title[:40] }}{% if lot.auction_title|length > 40 %}...{% endif %}
                                    </a>
                                </p>
                            </div>
                            <div class="col-md-2 text-center">
                                <span class="badge bg-{{ lot.status_class }} fs-6">
                                    {{ lot.timetable_status }}
                                </span>
                            </div>
                            <div class="col-md-2 text-center">
                                <div class="text-success fw-bold fs-5">€{{ lot.current_price }}</div>
                                <small class="text-muted">Current Bid</small>
                            </div>
                            <div class="col-md-2 text-end">
                                <div class="countdown text-primary" data-closing-time="{{ lot.closing_time.isoformat() }}">
                                    Calculating...
                                </div>
                                <div class="text-muted small">
                                    {{ lot.closing_time.strftime('%Y-%m-%d %H:%M:%S') }}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No lots closing in the next {{ hours }} hours</h5>
                        <p class="text-muted">Check back later or adjust the time range.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Update countdown timers
        function updateCountdowns() {
            const now = new Date();
            document.querySelectorAll('[data-closing-time]').forEach(function(element) {
                const closingTime = new Date(element.dataset.closingTime);
                const diff = closingTime - now;
                
                if (diff > 0) {
                    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
                    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((diff % (1000 * 60)) / 1000);
                    
                    let countdown = '';
                    if (days > 0) {
                        countdown = `${days}d ${hours}h ${minutes}m`;
                    } else if (hours > 0) {
                        countdown = `${hours}h ${minutes}m ${seconds}s`;
                    } else if (minutes > 0) {
                        countdown = `${minutes}m ${seconds}s`;
                    } else {
                        countdown = `${seconds}s`;
                    }
                    
                    element.textContent = countdown;
                    
                    // Update color based on time remaining
                    if (diff < 300000) { // Less than 5 minutes
                        element.className = 'countdown text-danger fw-bold';
                    } else if (diff < 3600000) { // Less than 1 hour
                        element.className = 'countdown text-warning fw-bold';
                    } else {
                        element.className = 'countdown text-primary';
                    }
                } else {
                    element.textContent = 'CLOSED';
                    element.className = 'countdown text-secondary';
                }
            });
        }
        
        // Update countdowns every second
        setInterval(updateCountdowns, 1000);
        updateCountdowns(); // Initial call
        
        // Auto-refresh every 60 seconds
        setTimeout(function() {
            location.reload();
        }, 60000);
    </script>
</body>
</html>
