{% extends "base.html" %}

{% block title %}Auctions - Auction Scraper{% endblock %}

{% block content %}
<div class="col-12">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-list"></i> All Auctions</h1>
        <button class="btn btn-primary" onclick="triggerAuctionDiscovery()">
            <i class="bi bi-search"></i> Discover More Auctions
        </button>
    </div>

    {% if auctions %}
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Title</th>
                                <th>Start Time</th>
                                <th>Lots</th>
                                <th>Status</th>
                                <th>Stage</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for auction in auctions %}
                            <tr>
                                <td>
                                    <strong>{{ auction.auction_id }}</strong>
                                </td>
                                <td>
                                    <a href="{{ url_for('auction_details', auction_id=auction.auction_id) }}" class="text-decoration-none">
                                        {{ auction.title }}
                                    </a>
                                    {% if auction.location %}
                                        <br><small class="text-muted"><i class="bi bi-geo-alt"></i> {{ auction.location }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if auction.start_time %}
                                        {{ auction.start_time | datetime_format('%Y-%m-%d %H:%M') }}
                                    {% else %}
                                        <span class="text-muted">TBD</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ auction.lot_count }}</span>
                                </td>
                                <td>
                                    <span class="badge {{ auction.status | status_badge }}">{{ auction.status }}</span>
                                </td>
                                <td>
                                    <span class="badge {{ auction.lifecycle_stage | status_badge }} status-badge">
                                        {{ auction.lifecycle_stage.replace('_', ' ').title() }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('auction_details', auction_id=auction.auction_id) }}" class="btn btn-outline-info">
                                            <i class="bi bi-eye"></i> View
                                        </a>
                                        <button class="btn btn-outline-primary" onclick="triggerLotDiscovery({{ auction.auction_id }})">
                                            <i class="bi bi-search"></i> Lots
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    {% else %}
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="bi bi-inbox fs-1 text-muted"></i>
                <h3 class="mt-3">No Auctions Found</h3>
                <p class="text-muted">No auctions have been discovered yet. Click "Discover More Auctions" to start scraping.</p>
                <button class="btn btn-primary" onclick="triggerAuctionDiscovery()">
                    <i class="bi bi-search"></i> Discover Auctions
                </button>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
