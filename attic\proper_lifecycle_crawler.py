#!/usr/bin/env python3
"""
Proper Lifecycle Crawler for Aurena Auctions
Implements the exact workflow specified:
1. Fetch all auctions with starting time and lot count
2. Fetch lot details when prompted or auction about to begin  
3. Fetch offer history when prompted or before closing
4. Check after closing for extensions until truly closed

Uses only real data, no dummy/simulated data.
"""

import sqlite3
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Optional
import logging
from auction_crawler import AuctionCrawler
from bid_fetcher import BidFetcher

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProperLifecycleCrawler:
    """Implements proper auction lifecycle workflow"""
    
    def __init__(self, db_path: str = "aurena_auctions.db"):
        self.db_path = db_path
        self.auction_crawler = AuctionCrawler(db_path)
        self.bid_fetcher = BidFetcher(db_path)
        self.init_lifecycle_database()
    
    def init_lifecycle_database(self):
        """Initialize database with lifecycle tracking"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Add lifecycle columns to existing tables
            try:
                cursor.execute("ALTER TABLE auctions ADD COLUMN lifecycle_stage TEXT DEFAULT 'discovered'")
            except sqlite3.OperationalError:
                pass  # Column already exists
            
            try:
                cursor.execute("ALTER TABLE lots ADD COLUMN lifecycle_stage TEXT DEFAULT 'discovered'")
            except sqlite3.OperationalError:
                pass  # Column already exists
            
            try:
                cursor.execute("ALTER TABLE lots ADD COLUMN bids_fetched BOOLEAN DEFAULT FALSE")
            except sqlite3.OperationalError:
                pass  # Column already exists
            
            # Lifecycle log table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS lifecycle_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    auction_id INTEGER,
                    lot_id INTEGER,
                    action TEXT NOT NULL,
                    status TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    details TEXT,
                    FOREIGN KEY (auction_id) REFERENCES auctions (auction_id),
                    FOREIGN KEY (lot_id) REFERENCES lots (lot_id)
                )
            """)
            
            conn.commit()
            logger.info("Lifecycle database initialized")
    
    def step1_fetch_all_auctions(self) -> List[Dict]:
        """
        STEP 1: Fetch all auctions together with starting time and lot count
        This is the entry point - discovers all available auctions
        """
        logger.info("🔍 STEP 1: Fetching all auctions with starting time and lot count")
        
        try:
            # Use the working auction crawler
            auctions = self.auction_crawler.fetch_active_auctions()
            
            if auctions:
                # Convert to dict format and save
                auction_dicts = []
                for auction in auctions:
                    auction_dict = {
                        'auction_id': auction.auction_id,
                        'title': auction.title,
                        'start_time': auction.start_time.isoformat(),
                        'end_time': auction.end_time.isoformat(),
                        'lot_count': auction.lot_count,
                        'location': auction.location,
                        'status': auction.status,
                        'lifecycle_stage': 'discovered'
                    }
                    auction_dicts.append(auction_dict)
                
                # Save with lifecycle tracking
                self._save_auctions_with_lifecycle(auction_dicts)
                
                logger.info(f"✅ STEP 1 COMPLETE: Discovered {len(auctions)} auctions")
                return auction_dicts
            else:
                logger.warning("No auctions found")
                return []
                
        except Exception as e:
            logger.error(f"Error in Step 1: {e}")
            return []
    
    def step2_fetch_auction_lots(self, auction_id: int, reason: str = "manual") -> List[Dict]:
        """
        STEP 2: Fetch all lot details for an auction if prompted through dashboard 
        or if the auction is about to begin
        """
        logger.info(f"🔍 STEP 2: Fetching lot details for auction {auction_id} (reason: {reason})")
        
        try:
            # Check if already fetched
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT lifecycle_stage FROM auctions WHERE auction_id = ?", (auction_id,))
                result = cursor.fetchone()
                
                if result and result[0] == 'lots_fetched':
                    logger.info(f"Lots already fetched for auction {auction_id}")
                    cursor.execute("SELECT * FROM lots WHERE auction_id = ?", (auction_id,))
                    return [dict(row) for row in cursor.fetchall()]
            
            # Use the working lot extraction
            lots = self.auction_crawler.extract_lots_from_auction(auction_id)
            
            if lots:
                # Convert to dict format
                lot_dicts = []
                for lot in lots:
                    lot_dict = {
                        'lot_id': lot.lot_id,
                        'auction_id': lot.auction_id,
                        'lot_number': lot.lot_number,
                        'title': lot.title,
                        'description': lot.description,
                        'starting_price': lot.starting_price,
                        'current_price': lot.current_price,
                        'closing_time': lot.closing_time.isoformat(),
                        'status': lot.status,
                        'bid_count': lot.bid_count,
                        'images': lot.images,
                        'lifecycle_stage': 'details_fetched',
                        'bids_fetched': False
                    }
                    lot_dicts.append(lot_dict)
                
                # Save lots and update auction lifecycle stage
                self._save_lots_with_lifecycle(lot_dicts, auction_id, reason)
                
                logger.info(f"✅ STEP 2 COMPLETE: Fetched {len(lots)} lots for auction {auction_id}")
                return lot_dicts
            else:
                logger.warning(f"No lots found for auction {auction_id}")
                return []
                
        except Exception as e:
            logger.error(f"Error in Step 2 for auction {auction_id}: {e}")
            return []
    
    def step3_fetch_lot_bids(self, lot_id: int, reason: str = "manual") -> List[Dict]:
        """
        STEP 3: Fetch offer history for each lot once if prompted through dashboard 
        or shortly before it closes
        """
        logger.info(f"🔍 STEP 3: Fetching offer history for lot {lot_id} (reason: {reason})")
        
        try:
            # Check if already fetched recently
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT bids_fetched, title FROM lots WHERE lot_id = ?", (lot_id,))
                result = cursor.fetchone()
                
                if not result:
                    logger.error(f"Lot {lot_id} not found in database")
                    return []
                
                bids_fetched, lot_title = result
                
                if bids_fetched and reason != "closing_check":
                    logger.info(f"Bids already fetched for lot {lot_id}")
                    cursor.execute("SELECT * FROM real_bids WHERE lot_id = ? ORDER BY timestamp DESC", (lot_id,))
                    return [dict(row) for row in cursor.fetchall()]
            
            # Get auction_id for this lot
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT auction_id FROM lots WHERE lot_id = ?", (lot_id,))
                result = cursor.fetchone()
                auction_id = result[0] if result else 0
            
            # Fetch bid history using the working bid fetcher
            success = self.bid_fetcher.fetch_and_save_lot_bids(lot_id, auction_id, lot_title)
            
            if success:
                # Get the saved bids
                with sqlite3.connect(self.db_path) as conn:
                    conn.row_factory = sqlite3.Row
                    cursor = conn.cursor()
                    cursor.execute("SELECT * FROM real_bids WHERE lot_id = ? ORDER BY timestamp DESC", (lot_id,))
                    bids = [dict(row) for row in cursor.fetchall()]
                
                # Update lot lifecycle stage
                self._update_lot_bids_fetched(lot_id, reason)
                
                logger.info(f"✅ STEP 3 COMPLETE: Fetched {len(bids)} bids for lot {lot_id}")
                return bids
            else:
                logger.warning(f"No bids found for lot {lot_id}")
                self._log_lifecycle_action(auction_id, lot_id, 'bids_fetch_attempted', 'no_data', reason)
                return []
                
        except Exception as e:
            logger.error(f"Error in Step 3 for lot {lot_id}: {e}")
            return []
    
    def step4_check_lot_after_closing(self, lot_id: int) -> Dict:
        """
        STEP 4: Check again shortly after closing time. If time has been extended 
        repeat until it is closed
        """
        logger.info(f"🔍 STEP 4: Checking lot {lot_id} after closing for extensions")
        
        try:
            # Get current lot info
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM lots WHERE lot_id = ?", (lot_id,))
                lot_data = cursor.fetchone()
                
                if not lot_data:
                    logger.error(f"Lot {lot_id} not found")
                    return {}
                
                lot_dict = dict(lot_data)
            
            # Check if lot is actually closed by fetching fresh bid data
            fresh_bids = self.step3_fetch_lot_bids(lot_id, "closing_check")
            
            # Compare closing times to detect extensions
            original_closing = datetime.fromisoformat(lot_dict['closing_time'])
            current_time = datetime.now()
            
            if current_time < original_closing:
                # Lot is still active
                status = "extended"
                logger.info(f"Lot {lot_id} appears to have been extended")
            else:
                # Lot should be closed
                status = "closed"
                logger.info(f"Lot {lot_id} is confirmed closed")
            
            # Log the check
            self._log_lifecycle_action(
                lot_dict['auction_id'], lot_id, 'closing_check', status, 
                f"Checked at {current_time.isoformat()}"
            )
            
            return {
                'lot_id': lot_id,
                'status': status,
                'check_time': current_time.isoformat(),
                'bid_count': len(fresh_bids)
            }
            
        except Exception as e:
            logger.error(f"Error in Step 4 for lot {lot_id}: {e}")
            return {}
    
    def _save_auctions_with_lifecycle(self, auctions: List[Dict]):
        """Save auctions with lifecycle tracking"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                current_time = datetime.now().isoformat()
                
                for auction in auctions:
                    cursor.execute("""
                        INSERT OR REPLACE INTO auctions 
                        (auction_id, title, starting_date, ending_date, lot_count, 
                         location_city, status_id, lifecycle_stage, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        auction['auction_id'], auction['title'], auction['start_time'],
                        auction['end_time'], auction['lot_count'], auction['location'],
                        1, auction['lifecycle_stage'], current_time, current_time
                    ))
                    
                    # Log lifecycle action
                    cursor.execute("""
                        INSERT INTO lifecycle_log (auction_id, action, status, timestamp, details)
                        VALUES (?, ?, ?, ?, ?)
                    """, (auction['auction_id'], 'auction_discovered', 'success', current_time, 
                          f"Found {auction['lot_count']} lots"))
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"Error saving auctions: {e}")
    
    def _save_lots_with_lifecycle(self, lots: List[Dict], auction_id: int, reason: str):
        """Save lots with lifecycle tracking"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                current_time = datetime.now().isoformat()
                
                for lot in lots:
                    cursor.execute("""
                        INSERT OR REPLACE INTO lots 
                        (lot_id, auction_id, lot_number, title, description, starting_price,
                         current_price, closing_time, status, bid_count, images, 
                         lifecycle_stage, bids_fetched, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        lot['lot_id'], lot['auction_id'], lot['lot_number'], lot['title'],
                        lot['description'], lot['starting_price'], lot['current_price'],
                        lot['closing_time'], lot['status'], lot['bid_count'], 
                        str(lot['images']), lot['lifecycle_stage'], lot['bids_fetched'],
                        current_time, current_time
                    ))
                
                # Update auction lifecycle stage
                cursor.execute("""
                    UPDATE auctions SET lifecycle_stage = 'lots_fetched', updated_at = ?
                    WHERE auction_id = ?
                """, (current_time, auction_id))
                
                # Log lifecycle action
                cursor.execute("""
                    INSERT INTO lifecycle_log (auction_id, action, status, timestamp, details)
                    VALUES (?, ?, ?, ?, ?)
                """, (auction_id, 'lots_fetched', 'success', current_time, 
                      f"Fetched {len(lots)} lots, reason: {reason}"))
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"Error saving lots: {e}")
    
    def _update_lot_bids_fetched(self, lot_id: int, reason: str):
        """Update lot bids fetched status"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                current_time = datetime.now().isoformat()
                
                cursor.execute("""
                    UPDATE lots SET bids_fetched = TRUE, lifecycle_stage = 'bids_fetched', updated_at = ?
                    WHERE lot_id = ?
                """, (current_time, lot_id))
                
                # Get auction_id
                cursor.execute("SELECT auction_id FROM lots WHERE lot_id = ?", (lot_id,))
                result = cursor.fetchone()
                auction_id = result[0] if result else 0
                
                # Log lifecycle action
                cursor.execute("""
                    INSERT INTO lifecycle_log (auction_id, lot_id, action, status, timestamp, details)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (auction_id, lot_id, 'bids_fetched', 'success', current_time, f"Reason: {reason}"))
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"Error updating lot bids status: {e}")
    
    def _log_lifecycle_action(self, auction_id: int, lot_id: int, action: str, status: str, details: str = ""):
        """Log lifecycle actions"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO lifecycle_log (auction_id, lot_id, action, status, timestamp, details)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (auction_id, lot_id, action, status, datetime.now().isoformat(), details))
                conn.commit()
        except Exception as e:
            logger.warning(f"Error logging lifecycle action: {e}")

def test_proper_lifecycle():
    """Test the proper lifecycle workflow"""
    print("🧪 TESTING PROPER LIFECYCLE WORKFLOW")
    print("=" * 60)
    
    crawler = ProperLifecycleCrawler()
    
    # STEP 1: Fetch all auctions
    print("\n🔍 STEP 1: Fetching all auctions...")
    auctions = crawler.step1_fetch_all_auctions()
    print(f"   ✅ Found {len(auctions)} auctions")
    
    if auctions:
        # Show sample
        sample_auction = auctions[0]
        print(f"   📋 Sample: {sample_auction['title'][:50]}... ({sample_auction['lot_count']} lots)")
        
        # STEP 2: Fetch lots for first auction
        auction_id = sample_auction['auction_id']
        print(f"\n🔍 STEP 2: Fetching lots for auction {auction_id}...")
        lots = crawler.step2_fetch_auction_lots(auction_id, "test")
        print(f"   ✅ Found {len(lots)} lots")
        
        if lots:
            # Show sample
            sample_lot = lots[0]
            print(f"   📋 Sample: Lot {sample_lot['lot_number']}: {sample_lot['title'][:40]}...")
            
            # STEP 3: Fetch bids for first lot
            lot_id = sample_lot['lot_id']
            print(f"\n🔍 STEP 3: Fetching bids for lot {lot_id}...")
            bids = crawler.step3_fetch_lot_bids(lot_id, "test")
            print(f"   ✅ Found {len(bids)} bids")
            
            if bids:
                print(f"   📋 Latest bid: €{bids[0]['amount']//100} on {bids[0]['bid_date']}")
    
    print(f"\n🎉 PROPER LIFECYCLE TEST COMPLETE")
    return len(auctions) > 0

if __name__ == "__main__":
    test_proper_lifecycle()
