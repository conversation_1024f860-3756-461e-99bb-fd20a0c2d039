# 🎯 Aurena Auction Crawler - Clean & Streamlined

A powerful, real-time web crawler for Aurena auction sites that extracts **actual auction and lot data** with a clean web dashboard.

## ✅ **WORKING FEATURES**

- **✅ Real Lot Data Extraction**: Successfully extracts all lot information from auction pages
- **✅ Price Tracking**: Monitors current bids in whole euros (€26, €8, etc.)
- **✅ Closing Time Tracking**: Accurate lot closing times with countdown
- **✅ Web Dashboard**: Clean Bootstrap interface showing auction statistics
- **✅ Database Storage**: SQLite database with proper data persistence
- **✅ Image URLs**: Extracts product images from S3 storage
- **✅ Lot Details**: Complete lot descriptions, titles, and metadata
- **Multi-language Support**: Handles German and English auction descriptions
- **Data Storage**: SQLite database for persistent storage with structured tables
- **Export Capabilities**: CSV export functionality for further analysis

### 🔥 Real-Time Bid Tracking (NEW!)
- **Precision Timing**: Monitors lot closing times and fetches bid histories exactly 5 seconds before closing
- **Time Extension Detection**: Automatically detects when auctions are extended due to last-minute bids (within 60 seconds)
- **Complete Bid Collection**: Ensures no bids are missed by fetching again after closing to verify completeness
- **Automated Scheduling**: Low-frequency auction crawling with high-precision lot monitoring
- **Async Processing**: Handles multiple simultaneous lot closings efficiently
- **Retry Logic**: Continues monitoring until lots are truly closed or maximum time exceeded

### 🌐 Web Dashboard Interface (NEW!)
- **Real-Time Status**: Live system monitoring with auto-refresh every 30 seconds
- **Hierarchical Navigation**: Interactive auction tree with expandable lot details
- **Upcoming Closings**: Timeline view of lots closing in the next 24-48 hours
- **Monitoring Tracking**: Visual status badges for lot monitoring states (Scheduled, Pre-Close, Extended, Completed)
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **RESTful API**: Complete API endpoints for integration with other systems

## 📁 Project Structure

```
AurenaWebCrawler/
├── aurena_crawler.py      # Main auction crawler
├── lot_tracker.py         # Real-time lot monitoring and bid tracking
├── auction_scheduler.py   # High-level scheduling and orchestration
├── realtime_tracker.py    # Command-line interface for real-time system
├── web_dashboard.py       # Flask web interface and API server
├── models.py             # Data models (Auction, Lot, Bid, etc.)
├── data_storage.py       # Database operations and storage
├── main.py              # Basic crawler interface
├── test_realtime.py     # Test suite for real-time system
├── test_web_interface.py # Test suite for web interface
├── templates/
│   └── dashboard.html    # Web dashboard HTML template
├── requirements.txt     # Python dependencies
└── README.md           # This documentation
```

## 🎯 Real-Time System Architecture

The system operates on two levels with corrected auction/lot terminology:

### 1. Low-Frequency Auction Monitoring
- **Frequency**: Every 6 hours (configurable)
- **Purpose**: Discover new auctions and update timetables
- **Data**: Auction metadata, lot counts, lot_closing_start_times
- **Key Point**: Auctions are sorted by official timestamp (starting_date), not auction_id

### 2. High-Precision Lot Tracking
- **Trigger**: 5 seconds before each lot's closing time
- **Process**:
  1. Fetch offer/bid history pre-close
  2. Wait for closing time
  3. Fetch offer/bid history post-close (after 10 seconds)
  4. Detect time extensions (60-second rule: bids in last 60 seconds extend closing)
  5. Reschedule if extended, complete if truly closed
- **Data**: Individual offers/bids with timestamps, amounts, bidder IDs
- **Key Point**: Each lot holds a list of offers; auction finishes when ALL lots are closed

### 🔑 Key Concepts
- **Lot = Product**: Same thing, we use "Lot" terminology
- **Auction Timing**: lot_closing_start_time ≠ auction finished time
- **Auction Finished**: Only when ALL lots in auction are closed
- **60-Second Rule**: Lot closing times can be extended
- **Sorting**: Always by official auction timestamp (starting_date), not auction_id

## 🛠 Installation

1. Clone or download this repository
2. Install required dependencies:

```bash
pip install -r requirements.txt
```

## 📖 Usage

### Basic Auction Crawling

```bash
# Run single crawl
python main.py

# View auction timetable
python main.py --timetable

# Export to CSV
python main.py --export-csv

# View crawl history
python main.py --history
```

### 🔥 Real-Time Bid Tracking

```bash
# Start the real-time tracking system
python realtime_tracker.py --start

# Check system status
python realtime_tracker.py --status

# View monitoring dashboard
python realtime_tracker.py --dashboard

# Show upcoming lot closings
python realtime_tracker.py --upcoming
```

### 🌐 Web Dashboard Interface

```bash
# Start the web dashboard
python web_dashboard.py

# Start with custom settings
python web_dashboard.py --host 0.0.0.0 --port 8080 --debug

# Test the web interface
python test_web_interface.py
```

**Web Interface Features:**
- 📊 **Real-time Dashboard**: System status, auction counts, success rates
- 🌳 **Interactive Tree View**: Navigate auctions and lots hierarchically
- ⏰ **Upcoming Closings**: Timeline of lots closing in next 24-48 hours
- 🎯 **Monitoring Status**: Live tracking of bid collection activities
- 📱 **Responsive Design**: Works on desktop, tablet, and mobile
- 🔄 **Auto-refresh**: Updates every 30 seconds automatically

**Access the dashboard at:** `http://127.0.0.1:5000`

### Example Real-Time Session

```bash
# Terminal 1: Start the tracker
python realtime_tracker.py --start
# 🚀 Starting Aurena Real-Time Auction Tracker
# ✅ Real-time tracker started successfully!
# The system will now:
#   • Crawl auctions every 6 hours
#   • Monitor lot closing times
#   • Collect bid histories 5 seconds before closing
#   • Handle time extensions automatically

# Terminal 2: Monitor status
python realtime_tracker.py --dashboard
# 📈 Aurena Auction Dashboard
# 🖥️  SYSTEM STATUS: 🟢 Running
# 🎯 MONITORING STATUS: 15 active schedules, 3 running tasks
# ⏰ NEXT CLOSINGS:
#   🏷️  Lot 148580042: BMW 320d Touring...
#   Closes in 2.3h | €15,500 | 12 bids
```

## 📊 Data Structure

### Auction-Level Data
- **Basic Info**: Auction ID, status, type (online/physical), lot count
- **Timing**: Start/end dates, lot_closing_start_time, payment deadlines, inspection/distribution schedules
- **Location**: Country, state, city, street address, GPS coordinates
- **Content**: Titles, descriptions (multi-language), categories, images
- **Contact**: Organizer details, phone numbers, email addresses
- **Key Concept**: Auction is finished only when ALL lots are closed, not when lot_closing_start_time is reached

### 🎯 Lot-Level Data (Real-Time)
- **Identification**: Lot ID, auction ID, lot number
- **Details**: Title, description, category, condition, images
- **Pricing**: Starting price, current price, bid count
- **Timing**: Original closing time, current closing time (with 60-second rule extensions)
- **Status**: Active, extended, closed
- **Offers**: List of bids/offers for this lot

### 💰 Bid/Offer-Level Data (Real-Time)
- **Identification**: Bid ID, lot ID, auction ID
- **Financial**: Bid amount, bid type (regular/auto/reserve)
- **Timing**: Precise timestamp of bid placement
- **Status**: Winning/outbid status, anonymized bidder ID
- **Key Concept**: Each lot holds a list of offers/bids

## 🗄️ Database Schema

### Core Tables
- `auctions`: Main auction data with timetables
- `lots`: Individual lot information with closing times
- `bids`: Complete bid history with timestamps
- `lot_schedules`: Monitoring schedules and status
- `bid_collection_log`: Tracking of bid collection activities

### Timing Tables
- `inspection_dates`: Viewing/inspection schedules
- `distribution_dates`: Pickup/distribution schedules
- `crawl_history`: System activity and success tracking

## ⚙️ Technical Implementation

### Data Extraction Methods

#### Auction Discovery
1. **HTTP Requests**: Fetches auction listing pages with proper headers
2. **HTML Entity Decoding**: Handles `&q;`, `&l;`, `&g;` encoded JSON data
3. **JavaScript Parsing**: Extracts embedded auction objects from package data
4. **Data Transformation**: Converts to structured Python objects

#### Real-Time Bid Tracking
1. **Async HTTP**: Uses aiohttp for concurrent bid history requests
2. **Precision Timing**: asyncio-based scheduling for exact timing control
3. **Extension Detection**: Compares bid timestamps to detect time extensions
4. **Retry Logic**: Continues monitoring until lots are definitively closed

### Key Components

#### Core Classes
- **AurenaCrawler**: HTTP-based auction data extraction
- **LotTracker**: Real-time lot monitoring and bid collection
- **AuctionScheduler**: High-level orchestration and scheduling
- **AuctionDataStorage**: Database operations and persistence

#### Data Models
- **Auction**: Complete auction information with timetables
- **Lot**: Individual lot details with closing times
- **Bid**: Individual bid records with precise timestamps
- **LotSchedule**: Monitoring schedules and status tracking

## 📈 Example Output

### Basic Timetable
```
=== AURENA AUCTION TIMETABLE ===
Generated: 2025-08-26 15:30:15
Total auctions: 155
--------------------------------------------------------------------------------
ID: 14858
Title: Thorwa Metalltechnik GmbH
Start: 2025-09-09 09:00
End: 2025-09-11 09:00
Location: Liebenfels, KTN
Lots: 31
Type: 0 | Status: 8
----------------------------------------
```

### Real-Time Dashboard
```
📈 Aurena Auction Dashboard
============================================================
🖥️  SYSTEM STATUS
Scheduler: 🟢 Running | Total Auctions: 155 | Success Rate: 100.0%

🎯 MONITORING STATUS
Active Schedules: 47 | Running Tasks: 12

📋 LOT MONITORING DETAILS
🔥 Lot 148580042: pre_close (closes in 0.1h, 0 extensions)
⏰ Lot 148580043: scheduled (closes in 2.3h, 0 extensions)
✅ Lot 148580041: completed (closes in -0.2h, 2 extensions)

⏰ NEXT CLOSINGS
🏷️  Lot 148580044: BMW 320d Touring Automatik...
   Closes in 1.2h | €15,500 | 12 bids
```

### Bid Collection Log
```
2025-08-26 15:53:24 - LotTracker - INFO - Lot 148580042: Starting pre-close monitoring
2025-08-26 15:53:24 - LotTracker - INFO - Fetched 8 bids for lot 148580042
2025-08-26 15:53:28 - LotTracker - INFO - Lot 148580042: Starting post-close verification
2025-08-26 15:53:38 - LotTracker - INFO - Lot 148580042: Extended to 2025-08-26 15:54:38
2025-08-26 15:54:48 - LotTracker - INFO - Lot 148580042: Monitoring completed
```

## 🚀 Advanced Features

### Implemented
- ✅ **Real-Time Bid Tracking**: Complete bid history collection with precise timing
- ✅ **Time Extension Handling**: Automatic detection and rescheduling
- ✅ **Concurrent Monitoring**: Handle multiple simultaneous lot closings
- ✅ **Data Integrity**: Verification of complete bid collection
- ✅ **Web Dashboard Interface**: Interactive monitoring and navigation
- ✅ **RESTful API**: Complete API endpoints for system integration
- ✅ **Automated Scheduling**: Low-frequency discovery, high-precision tracking

### Future Enhancements
- 🔄 **API Integration**: Direct API access for more reliable data
- 📱 **Mobile Notifications**: Push alerts for specific lots or price thresholds
- 📊 **Analytics**: Bidding pattern analysis and price trend predictions
- 🤖 **ML Integration**: Predict auction outcomes and optimal bid timing

## ⚖️ Legal Considerations

This system is designed for educational and research purposes. When using:

- **Respect Rate Limits**: The system implements reasonable delays and respects server capacity
- **Terms of Service**: Ensure compliance with aurena.at's terms of service
- **Data Usage**: Use collected data responsibly and in accordance with applicable laws
- **API Access**: Consider reaching out to aurena.at for official API access for production use
- **Privacy**: Bidder information is anonymized in the system

## 📦 Dependencies

### Core Libraries
- `requests`: HTTP client for web scraping
- `beautifulsoup4`: HTML parsing and data extraction
- `pandas`: Data manipulation and analysis
- `aiohttp`: Async HTTP for real-time operations
- `schedule`: Task scheduling and automation

### Built-in Modules
- `sqlite3`: Database operations
- `asyncio`: Asynchronous programming
- `json`: JSON data handling
- `datetime`: Time and date operations
- `threading`: Concurrent execution

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Test all components
python test_realtime.py

# Test basic crawler only
python test_crawler.py
```

## 📄 License

This project is for educational and research purposes. Please ensure compliance with applicable laws and website terms of service when using this system.

---

## 🎯 Quick Start Summary

1. **Install**: `pip install -r requirements.txt`
2. **Basic Crawl**: `python main.py`
3. **Real-Time**: `python realtime_tracker.py --start`
4. **Web Dashboard**: `python web_dashboard.py`
5. **Monitor**: `python realtime_tracker.py --dashboard`

**Perfect for**: Auction research, market analysis, bid timing studies, and educational exploration of real-time data collection systems.