#!/usr/bin/env python3
"""
Comprehensive Test Suite for Auction Scraper System
Tests all components with real scraped data only - NO hardcoded data or assumptions.
"""

import sys
import unittest
import tempfile
import shutil
from pathlib import Path
import logging
from datetime import datetime, timedelta

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.database.manager import DatabaseManager
from src.database.models import Auction, Lot, Bid
from src.scraper.auction_list_scraper import AuctionListScraper
from src.scraper.lot_list_scraper import LotListScraper
from src.scraper.lot_details_scraper import LotDetailsScraper
from src.scheduler.auction_scheduler import AuctionScheduler
from src.web.dashboard import AuctionDashboard

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestAuctionScraper(unittest.TestCase):
    """Comprehensive test suite for auction scraper system"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.db_path = Path(self.test_dir) / "test_auction_scraper.db"
        self.db_manager = DatabaseManager(str(self.db_path))
        
        logger.info(f"Test database created at: {self.db_path}")
    
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_01_database_initialization(self):
        """Test database initialization and schema creation"""
        logger.info("🧪 Testing database initialization...")
        
        # Test database file creation
        self.assertTrue(self.db_path.exists(), "Database file should be created")
        
        # Test statistics on empty database
        stats = self.db_manager.get_statistics()
        self.assertIsInstance(stats, dict, "Statistics should return a dictionary")
        self.assertEqual(stats.get('total_auctions', 0), 0, "Empty database should have 0 auctions")
        
        logger.info("✅ Database initialization test passed")
    
    def test_02_auction_list_scraper(self):
        """Test auction list scraper with real data"""
        logger.info("🧪 Testing auction list scraper...")
        
        scraper = AuctionListScraper()
        
        # Test auction discovery - NO hardcoded data
        auctions_data = scraper.discover_auctions()
        
        self.assertIsInstance(auctions_data, list, "Auction discovery should return a list")
        logger.info(f"Discovered {len(auctions_data)} auctions")
        
        if auctions_data:
            # Validate auction data structure
            first_auction = auctions_data[0]
            self.assertIn('auction_id', first_auction, "Auction should have auction_id")
            self.assertIn('title', first_auction, "Auction should have title")
            self.assertIsInstance(first_auction['auction_id'], int, "Auction ID should be integer")
            self.assertIsInstance(first_auction['title'], str, "Title should be string")
            
            # Test saving to database
            auctions = []
            for data in auctions_data[:5]:  # Test with first 5 auctions
                auction = Auction(
                    auction_id=data['auction_id'],
                    title=data['title'],
                    start_time=data.get('start_time'),
                    status=data.get('status', 'active')
                )
                auctions.append(auction)
            
            success = self.db_manager.save_auctions(auctions)
            self.assertTrue(success, "Should successfully save auctions to database")
            
            # Verify database storage
            stats = self.db_manager.get_statistics()
            self.assertGreater(stats.get('total_auctions', 0), 0, "Database should contain auctions")
            
            logger.info("✅ Auction list scraper test passed")
        else:
            logger.warning("⚠️ No auctions discovered - this may indicate network issues or site changes")
    
    def test_03_lot_list_scraper(self):
        """Test lot list scraper with real auction data"""
        logger.info("🧪 Testing lot list scraper...")
        
        # First get some auctions
        auction_scraper = AuctionListScraper()
        auctions_data = auction_scraper.discover_auctions()
        
        if not auctions_data:
            self.skipTest("No auctions available for lot testing")
        
        # Test with first auction
        test_auction = auctions_data[0]
        auction_id = test_auction['auction_id']
        
        lot_scraper = LotListScraper()
        lots_data = lot_scraper.scrape_auction_lots(auction_id)
        
        self.assertIsInstance(lots_data, list, "Lot scraping should return a list")
        logger.info(f"Extracted {len(lots_data)} lots from auction {auction_id}")
        
        if lots_data:
            # Validate lot data structure
            first_lot = lots_data[0]
            self.assertIn('lot_id', first_lot, "Lot should have lot_id")
            self.assertIn('auction_id', first_lot, "Lot should have auction_id")
            self.assertIn('title', first_lot, "Lot should have title")
            self.assertEqual(first_lot['auction_id'], auction_id, "Lot should belong to correct auction")
            
            # Test saving to database
            lots = []
            for data in lots_data[:10]:  # Test with first 10 lots
                lot = Lot(
                    lot_id=data['lot_id'],
                    auction_id=data['auction_id'],
                    title=data.get('title', f"Lot {data['lot_id']}"),
                    current_price=data.get('current_price', 0),
                    closing_time=data.get('closing_time')
                )
                lots.append(lot)
            
            success = self.db_manager.save_lots(lots)
            self.assertTrue(success, "Should successfully save lots to database")
            
            # Verify database storage
            stats = self.db_manager.get_statistics()
            self.assertGreater(stats.get('total_lots', 0), 0, "Database should contain lots")
            
            logger.info("✅ Lot list scraper test passed")
        else:
            logger.warning(f"⚠️ No lots found for auction {auction_id}")
    
    def test_04_lot_details_scraper(self):
        """Test lot details scraper with real lot data"""
        logger.info("🧪 Testing lot details scraper...")
        
        # First get some lots
        auction_scraper = AuctionListScraper()
        auctions_data = auction_scraper.discover_auctions()
        
        if not auctions_data:
            self.skipTest("No auctions available for lot details testing")
        
        # Get lots from first auction
        test_auction = auctions_data[0]
        lot_scraper = LotListScraper()
        lots_data = lot_scraper.scrape_auction_lots(test_auction['auction_id'])
        
        if not lots_data:
            self.skipTest("No lots available for details testing")
        
        # Test with first lot
        test_lot = lots_data[0]
        lot_id = test_lot['lot_id']
        
        details_scraper = LotDetailsScraper()
        lot_details = details_scraper.scrape_lot_details(lot_id, include_bids=True)
        
        self.assertIsInstance(lot_details, dict, "Lot details should return a dictionary")
        logger.info(f"Extracted details for lot {lot_id}: {lot_details.get('title', 'Unknown')}")
        
        if lot_details:
            # Validate lot details structure
            self.assertIn('lot_id', lot_details, "Details should include lot_id")
            self.assertEqual(lot_details['lot_id'], lot_id, "Details should be for correct lot")
            
            # Test bid data if available
            if 'bids' in lot_details and lot_details['bids']:
                bids_data = lot_details['bids']
                self.assertIsInstance(bids_data, list, "Bids should be a list")
                
                # Validate bid structure
                first_bid = bids_data[0]
                self.assertIn('amount', first_bid, "Bid should have amount")
                self.assertIn('lot_id', first_bid, "Bid should have lot_id")
                self.assertIsInstance(first_bid['amount'], int, "Bid amount should be integer")
                
                logger.info(f"Found {len(bids_data)} bids for lot {lot_id}")
            
            logger.info("✅ Lot details scraper test passed")
        else:
            logger.warning(f"⚠️ No details found for lot {lot_id}")
    
    def test_05_scheduler_functionality(self):
        """Test scheduler functionality"""
        logger.info("🧪 Testing scheduler functionality...")
        
        scheduler = AuctionScheduler(self.db_manager)
        
        # Test scheduler initialization
        self.assertIsNotNone(scheduler, "Scheduler should initialize")
        self.assertFalse(scheduler.running, "Scheduler should not be running initially")
        
        # Test manual triggers
        success = scheduler.trigger_manual_auction_discovery()
        self.assertTrue(success, "Manual auction discovery should be triggerable")
        
        # Test scheduled tasks
        tasks = scheduler.get_scheduled_tasks()
        self.assertIsInstance(tasks, list, "Scheduled tasks should return a list")
        
        logger.info("✅ Scheduler functionality test passed")
    
    def test_06_web_dashboard_creation(self):
        """Test web dashboard creation"""
        logger.info("🧪 Testing web dashboard creation...")
        
        try:
            dashboard = AuctionDashboard(str(self.db_path))
            self.assertIsNotNone(dashboard, "Dashboard should initialize")
            self.assertIsNotNone(dashboard.app, "Dashboard should have Flask app")
            
            # Test Flask app configuration
            self.assertEqual(dashboard.app.config.get('JSON_SORT_KEYS'), False)
            
            logger.info("✅ Web dashboard creation test passed")
        except Exception as e:
            self.fail(f"Dashboard creation failed: {e}")
    
    def test_07_database_operations(self):
        """Test comprehensive database operations"""
        logger.info("🧪 Testing database operations...")
        
        # Test auction operations
        test_auction = Auction(
            auction_id=99999,  # Use high ID to avoid conflicts
            title="Test Auction",
            status="active"
        )
        
        success = self.db_manager.save_auction(test_auction)
        self.assertTrue(success, "Should save auction successfully")
        
        retrieved_auction = self.db_manager.get_auction(99999)
        self.assertIsNotNone(retrieved_auction, "Should retrieve saved auction")
        self.assertEqual(retrieved_auction.title, "Test Auction")
        
        # Test lot operations
        test_lot = Lot(
            lot_id=99999,
            auction_id=99999,
            title="Test Lot",
            current_price=100
        )
        
        success = self.db_manager.save_lot(test_lot)
        self.assertTrue(success, "Should save lot successfully")
        
        retrieved_lot = self.db_manager.get_lot(99999)
        self.assertIsNotNone(retrieved_lot, "Should retrieve saved lot")
        self.assertEqual(retrieved_lot.title, "Test Lot")
        
        # Test bid operations
        test_bid = Bid(
            lot_id=99999,
            auction_id=99999,
            amount=150,
            timestamp=datetime.now()
        )
        
        success = self.db_manager.save_bid(test_bid)
        self.assertTrue(success, "Should save bid successfully")
        
        bids = self.db_manager.get_bids_for_lot(99999)
        self.assertGreater(len(bids), 0, "Should retrieve saved bids")
        
        # Test lifecycle logging
        success = self.db_manager.log_lifecycle_action(
            99999, 99999, "test_action", "success", "Test details"
        )
        self.assertTrue(success, "Should log lifecycle action successfully")
        
        logs = self.db_manager.get_lifecycle_logs(limit=10)
        self.assertGreater(len(logs), 0, "Should retrieve lifecycle logs")
        
        logger.info("✅ Database operations test passed")
    
    def test_08_end_to_end_workflow(self):
        """Test complete end-to-end workflow"""
        logger.info("🧪 Testing end-to-end workflow...")
        
        try:
            # Step 1: Discover auctions
            auction_scraper = AuctionListScraper()
            auctions_data = auction_scraper.discover_auctions()
            
            if not auctions_data:
                logger.warning("⚠️ No auctions discovered - skipping end-to-end test")
                return
            
            # Step 2: Save auctions to database
            auctions = []
            for data in auctions_data[:3]:  # Test with first 3 auctions
                auction = Auction(
                    auction_id=data['auction_id'],
                    title=data['title'],
                    start_time=data.get('start_time'),
                    status=data.get('status', 'active'),
                    lifecycle_stage='discovered'
                )
                auctions.append(auction)
            
            success = self.db_manager.save_auctions(auctions)
            self.assertTrue(success, "Should save discovered auctions")
            
            # Step 3: Discover lots for first auction
            test_auction = auctions[0]
            lot_scraper = LotListScraper()
            lots_data = lot_scraper.scrape_auction_lots(test_auction.auction_id)
            
            if lots_data:
                # Step 4: Save lots to database
                lots = []
                for data in lots_data[:5]:  # Test with first 5 lots
                    lot = Lot(
                        lot_id=data['lot_id'],
                        auction_id=data['auction_id'],
                        title=data.get('title', f"Lot {data['lot_id']}"),
                        current_price=data.get('current_price', 0),
                        lifecycle_stage='lots_discovered'
                    )
                    lots.append(lot)
                
                success = self.db_manager.save_lots(lots)
                self.assertTrue(success, "Should save discovered lots")
                
                # Step 5: Get lot details for first lot
                if lots:
                    test_lot = lots[0]
                    details_scraper = LotDetailsScraper()
                    lot_details = details_scraper.scrape_lot_details(test_lot.lot_id, include_bids=True)
                    
                    if lot_details and lot_details.get('bids'):
                        # Step 6: Save bids to database
                        bids = []
                        for bid_data in lot_details['bids'][:10]:  # Test with first 10 bids
                            bid = Bid(
                                lot_id=bid_data['lot_id'],
                                auction_id=test_lot.auction_id,
                                amount=bid_data['amount'],
                                timestamp=bid_data.get('timestamp', datetime.now())
                            )
                            bids.append(bid)
                        
                        if bids:
                            success = self.db_manager.save_bids(bids)
                            self.assertTrue(success, "Should save discovered bids")
            
            # Step 7: Verify final statistics
            final_stats = self.db_manager.get_statistics()
            self.assertGreater(final_stats.get('total_auctions', 0), 0, "Should have auctions in database")
            
            logger.info("✅ End-to-end workflow test passed")
            logger.info(f"Final statistics: {final_stats}")
            
        except Exception as e:
            logger.error(f"End-to-end test failed: {e}")
            raise


def run_comprehensive_test():
    """Run comprehensive test suite"""
    print("=" * 80)
    print("🎯 COMPREHENSIVE AUCTION SCRAPER TEST SUITE")
    print("=" * 80)
    print("Testing all components with REAL scraped data only")
    print("NO hardcoded data or assumptions about data correctness")
    print("=" * 80)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestAuctionScraper)
    
    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 80)
    print("📊 TEST SUMMARY")
    print("=" * 80)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print("\n❌ FAILURES:")
        for test, traceback in result.failures:
            error_msg = traceback.split('AssertionError: ')[-1].split('\n')[0]
            print(f"  - {test}: {error_msg}")

    if result.errors:
        print("\n💥 ERRORS:")
        for test, traceback in result.errors:
            error_msg = traceback.split('\n')[-2]
            print(f"  - {test}: {error_msg}")
    
    if not result.failures and not result.errors:
        print("\n🎉 ALL TESTS PASSED!")
        print("The auction scraper system is working correctly with real data.")
    
    print("=" * 80)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
