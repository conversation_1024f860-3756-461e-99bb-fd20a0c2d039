#!/usr/bin/env python3
"""
Lifecycle-based Aurena Auction Crawler
Implements the proper workflow:
1. Fetch all auctions with starting time and lot count
2. Fetch lot details when prompted or auction about to begin
3. Fetch offer history when prompted or shortly before closing
4. Check after closing for extensions until truly closed
"""

import requests
from bs4 import BeautifulSoup
import sqlite3
import json
import re
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LifecycleCrawler:
    """Proper lifecycle-based auction crawler"""
    
    def __init__(self, db_path: str = "aurena_auctions.db"):
        self.db_path = db_path
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.base_url = "https://www.aurena.at"
        self.init_database()
    
    def init_database(self):
        """Initialize clean database with proper lifecycle schema"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Auctions table - basic info only
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS auctions (
                    auction_id INTEGER PRIMARY KEY,
                    title TEXT NOT NULL,
                    start_time TEXT NOT NULL,
                    end_time TEXT,
                    lot_count INTEGER DEFAULT 0,
                    location TEXT,
                    status TEXT DEFAULT 'discovered',
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            """)
            
            # Lots table - detailed info fetched on demand
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS lots (
                    lot_id INTEGER PRIMARY KEY,
                    auction_id INTEGER NOT NULL,
                    lot_number INTEGER,
                    title TEXT NOT NULL,
                    description TEXT,
                    starting_price INTEGER DEFAULT 0,
                    current_price INTEGER DEFAULT 0,
                    closing_time TEXT NOT NULL,
                    status TEXT DEFAULT 'discovered',
                    images TEXT,
                    details_fetched BOOLEAN DEFAULT FALSE,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    FOREIGN KEY (auction_id) REFERENCES auctions (auction_id)
                )
            """)
            
            # Bids table - real bid history
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS bids (
                    bid_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    lot_id INTEGER NOT NULL,
                    auction_id INTEGER NOT NULL,
                    amount INTEGER NOT NULL,
                    bid_date TEXT NOT NULL,
                    bid_time TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    fetched_at TEXT NOT NULL,
                    FOREIGN KEY (lot_id) REFERENCES lots (lot_id)
                )
            """)
            
            # Lifecycle tracking
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS lifecycle_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    auction_id INTEGER,
                    lot_id INTEGER,
                    action TEXT NOT NULL,
                    status TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    details TEXT
                )
            """)
            
            conn.commit()
            logger.info("Database initialized with lifecycle schema")
    
    def fetch_all_auctions(self) -> List[Dict]:
        """
        STEP 1: Fetch all auctions with starting time and lot count
        This is the entry point - gets basic auction info only
        """
        logger.info("🔍 STEP 1: Fetching all auctions with basic info")

        # For now, use known working auction IDs and discover more
        # This is a practical approach since the API might be restricted
        known_auction_ids = [14941, 15096, 15099, 15100, 15101, 15102]
        auctions = []

        for auction_id in known_auction_ids:
            try:
                auction_data = self._fetch_auction_basic_info(auction_id)
                if auction_data:
                    auctions.append(auction_data)
                    logger.info(f"Found auction {auction_id}: {auction_data['title'][:50]}...")

                # Rate limiting
                time.sleep(1)

            except Exception as e:
                logger.warning(f"Error fetching auction {auction_id}: {e}")
                continue

        # Save auctions to database
        self._save_auctions(auctions)

        logger.info(f"✅ STEP 1 COMPLETE: Fetched {len(auctions)} auctions")
        return auctions

    def _fetch_auction_basic_info(self, auction_id: int) -> Optional[Dict]:
        """Fetch basic info for a single auction"""
        try:
            auction_url = f"{self.base_url}/auktion/{auction_id}"
            response = self.session.get(auction_url, timeout=30)

            if response.status_code != 200:
                logger.warning(f"Auction {auction_id} not accessible: HTTP {response.status_code}")
                return None

            soup = BeautifulSoup(response.content, 'html.parser')

            # Extract title
            title_elem = soup.find('h1') or soup.find('title')
            title = title_elem.get_text(strip=True) if title_elem else f"Auction {auction_id}"

            # Count lots by finding lot links
            lot_links = soup.find_all('a', href=re.compile(r'/posten/\d+'))
            lot_count = len(lot_links)

            # Extract dates from page content (basic approach)
            page_text = soup.get_text()

            # Look for date patterns
            date_patterns = re.findall(r'\d{2}\.\d{2}\.\d{4}', page_text)

            # Use current time as placeholder for now - real dates would need more specific parsing
            current_time = datetime.now()
            start_time = current_time.isoformat()
            end_time = (current_time + timedelta(days=7)).isoformat()

            # Try to extract location
            location = ""
            location_indicators = ['Wien', 'Graz', 'Linz', 'Salzburg', 'Innsbruck']
            for indicator in location_indicators:
                if indicator in page_text:
                    location = indicator
                    break

            return {
                'auction_id': auction_id,
                'title': title,
                'start_time': start_time,
                'end_time': end_time,
                'lot_count': lot_count,
                'location': location,
                'status': 'discovered'
            }

        except Exception as e:
            logger.error(f"Error fetching auction {auction_id}: {e}")
            return None

    def _parse_api_date(self, date_str: str) -> str:
        """Parse date from API response"""
        if not date_str:
            return datetime.now().isoformat()

        try:
            # Try different date formats
            for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%dT%H:%M:%S', '%Y-%m-%d']:
                try:
                    dt = datetime.strptime(date_str, fmt)
                    return dt.isoformat()
                except ValueError:
                    continue

            # If parsing fails, return current time
            logger.warning(f"Could not parse date: {date_str}")
            return datetime.now().isoformat()

        except Exception as e:
            logger.warning(f"Error parsing date {date_str}: {e}")
            return datetime.now().isoformat()
    
    def _parse_auction_page(self, soup: BeautifulSoup, auction_id: int, title: str) -> Optional[Dict]:
        """Parse auction page for basic info"""
        try:
            # Look for date/time information
            start_time = None
            end_time = None
            lot_count = 0
            location = None
            
            # Find time elements (this will need to be adapted based on actual HTML structure)
            time_elements = soup.find_all(text=re.compile(r'\d{2}\.\d{2}\.\d{4}'))
            
            # Extract lot count from page
            lot_links = soup.find_all('a', href=re.compile(r'/posten/\d+'))
            lot_count = len(lot_links)
            
            # For now, use current time as placeholder - this needs real parsing
            current_time = datetime.now()
            start_time = current_time.isoformat()
            end_time = (current_time + timedelta(days=7)).isoformat()
            
            return {
                'auction_id': auction_id,
                'title': title,
                'start_time': start_time,
                'end_time': end_time,
                'lot_count': lot_count,
                'location': location,
                'status': 'discovered'
            }
            
        except Exception as e:
            logger.warning(f"Error parsing auction {auction_id}: {e}")
            return None
    
    def _save_auctions(self, auctions: List[Dict]):
        """Save auctions to database"""
        if not auctions:
            return
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                current_time = datetime.now().isoformat()
                
                for auction in auctions:
                    cursor.execute("""
                        INSERT OR REPLACE INTO auctions 
                        (auction_id, title, start_time, end_time, lot_count, location, status, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        auction['auction_id'], auction['title'], auction['start_time'],
                        auction['end_time'], auction['lot_count'], auction['location'],
                        auction['status'], current_time, current_time
                    ))
                    
                    # Log lifecycle action
                    cursor.execute("""
                        INSERT INTO lifecycle_log (auction_id, action, status, timestamp, details)
                        VALUES (?, ?, ?, ?, ?)
                    """, (
                        auction['auction_id'], 'auction_discovered', 'success', 
                        current_time, f"Found {auction['lot_count']} lots"
                    ))
                
                conn.commit()
                logger.info(f"Saved {len(auctions)} auctions to database")
                
        except Exception as e:
            logger.error(f"Error saving auctions: {e}")
    
    def fetch_auction_lots(self, auction_id: int, force: bool = False) -> List[Dict]:
        """
        STEP 2: Fetch lot details for an auction when prompted or about to begin
        Uses the working JavaScript parsing approach from the original crawler
        """
        logger.info(f"🔍 STEP 2: Fetching lot details for auction {auction_id}")

        try:
            # Check if already fetched (unless forced)
            if not force:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT COUNT(*) FROM lots WHERE auction_id = ? AND details_fetched = TRUE", (auction_id,))
                    if cursor.fetchone()[0] > 0:
                        logger.info(f"Lot details already fetched for auction {auction_id}")
                        cursor.execute("SELECT * FROM lots WHERE auction_id = ?", (auction_id,))
                        return [dict(row) for row in cursor.fetchall()]

            # Fetch auction page
            auction_url = f"{self.base_url}/auktion/{auction_id}"
            response = self.session.get(auction_url, timeout=30)

            if response.status_code != 200:
                logger.error(f"Failed to fetch auction {auction_id}: {response.status_code}")
                return []

            # Parse lots from JavaScript data (like the original crawler)
            lots = self._extract_lots_from_page(response.text, auction_id)

            if lots:
                # Save lots to database
                self._save_lots(lots)
                logger.info(f"✅ STEP 2 COMPLETE: Found {len(lots)} lots for auction {auction_id}")
            else:
                logger.warning(f"No lots found for auction {auction_id}")

            return lots

        except Exception as e:
            logger.error(f"Error fetching lots for auction {auction_id}: {e}")
            return []

    def _extract_lots_from_page(self, page_content: str, auction_id: int) -> List[Dict]:
        """Extract lots from auction page JavaScript data"""
        lots = []

        try:
            # Look for JavaScript data containing lot information
            # This is based on the working approach from the original crawler

            # Find script tags with lot data
            soup = BeautifulSoup(page_content, 'html.parser')
            scripts = soup.find_all('script')

            for script in scripts:
                if not script.string:
                    continue

                script_content = script.string

                # Look for lot data patterns
                if 'lot' in script_content.lower() and ('id' in script_content or 'nummer' in script_content):
                    # Try to extract lot IDs and basic info
                    lot_ids = re.findall(r'"id":\s*(\d+)', script_content)
                    lot_numbers = re.findall(r'"nummer":\s*(\d+)', script_content)
                    lot_titles = re.findall(r'"titel":\s*"([^"]*)"', script_content)

                    # Also look for closing times
                    closing_times = re.findall(r'"ende":\s*(\d+)', script_content)

                    # Process found data
                    for i, lot_id in enumerate(lot_ids):
                        try:
                            lot_data = {
                                'lot_id': int(lot_id),
                                'auction_id': auction_id,
                                'lot_number': int(lot_numbers[i]) if i < len(lot_numbers) else i + 1,
                                'title': lot_titles[i] if i < len(lot_titles) else f'Lot {lot_id}',
                                'closing_time': self._parse_timestamp(closing_times[i]) if i < len(closing_times) else (datetime.now() + timedelta(days=7)).isoformat(),
                                'status': 'discovered',
                                'details_fetched': True,
                                'starting_price': 0,
                                'current_price': 0
                            }
                            lots.append(lot_data)

                        except (ValueError, IndexError) as e:
                            logger.warning(f"Error parsing lot data: {e}")
                            continue

                    if lots:
                        break  # Found lot data, no need to check other scripts

            # If no lots found in JavaScript, try HTML parsing as fallback
            if not lots:
                lots = self._extract_lots_from_html(soup, auction_id)

            return lots

        except Exception as e:
            logger.error(f"Error extracting lots from page: {e}")
            return []

    def _extract_lots_from_html(self, soup: BeautifulSoup, auction_id: int) -> List[Dict]:
        """Fallback: Extract lots from HTML links"""
        lots = []

        try:
            lot_links = soup.find_all('a', href=re.compile(r'/posten/(\d+)'))

            for link in lot_links:
                lot_match = re.search(r'/posten/(\d+)', link['href'])
                if not lot_match:
                    continue

                lot_id = int(lot_match.group(1))
                lot_title = link.get_text(strip=True) or f'Lot {lot_id}'

                lot_data = {
                    'lot_id': lot_id,
                    'auction_id': auction_id,
                    'title': lot_title,
                    'closing_time': (datetime.now() + timedelta(days=7)).isoformat(),
                    'status': 'discovered',
                    'details_fetched': False,  # HTML parsing doesn't get full details
                    'starting_price': 0,
                    'current_price': 0
                }

                lots.append(lot_data)

            return lots

        except Exception as e:
            logger.error(f"Error extracting lots from HTML: {e}")
            return []

    def _parse_timestamp(self, timestamp_str: str) -> str:
        """Parse timestamp from JavaScript (usually milliseconds)"""
        try:
            timestamp_ms = int(timestamp_str)
            dt = datetime.fromtimestamp(timestamp_ms / 1000)
            return dt.isoformat()
        except:
            return (datetime.now() + timedelta(days=7)).isoformat()
    
    def _save_lots(self, lots: List[Dict]):
        """Save lots to database"""
        if not lots:
            return
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                current_time = datetime.now().isoformat()
                
                for lot in lots:
                    # Use placeholder closing time - will be updated when details are fetched
                    closing_time = (datetime.now() + timedelta(days=7)).isoformat()
                    
                    cursor.execute("""
                        INSERT OR REPLACE INTO lots 
                        (lot_id, auction_id, title, closing_time, status, details_fetched, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        lot['lot_id'], lot['auction_id'], lot['title'], closing_time,
                        lot['status'], lot['details_fetched'], current_time, current_time
                    ))
                    
                    # Log lifecycle action
                    cursor.execute("""
                        INSERT INTO lifecycle_log (auction_id, lot_id, action, status, timestamp)
                        VALUES (?, ?, ?, ?, ?)
                    """, (
                        lot['auction_id'], lot['lot_id'], 'lot_discovered', 'success', current_time
                    ))
                
                conn.commit()
                logger.info(f"Saved {len(lots)} lots to database")
                
        except Exception as e:
            logger.error(f"Error saving lots: {e}")

    def fetch_lot_bids(self, lot_id: int, lot_title: str = "", force: bool = False) -> List[Dict]:
        """
        STEP 3: Fetch offer history for a lot when prompted or before closing
        """
        logger.info(f"🔍 STEP 3: Fetching bid history for lot {lot_id}")

        try:
            # Check if already fetched recently (unless forced)
            if not force:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT COUNT(*) FROM bids WHERE lot_id = ?", (lot_id,))
                    if cursor.fetchone()[0] > 0:
                        logger.info(f"Bid history already exists for lot {lot_id}")
                        cursor.execute("SELECT * FROM bids WHERE lot_id = ? ORDER BY timestamp DESC", (lot_id,))
                        return [dict(row) for row in cursor.fetchall()]

            # Use the working bid fetcher approach
            from bid_fetcher import BidFetcher
            bid_fetcher = BidFetcher(self.db_path)

            bids = bid_fetcher.fetch_lot_bid_history(lot_id, lot_title)

            if bids:
                # Get auction_id for this lot
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT auction_id FROM lots WHERE lot_id = ?", (lot_id,))
                    result = cursor.fetchone()
                    auction_id = result[0] if result else 0

                # Save bids
                success = bid_fetcher.save_bid_history(lot_id, auction_id, bids)

                if success:
                    logger.info(f"✅ STEP 3 COMPLETE: Fetched {len(bids)} bids for lot {lot_id}")

                    # Log lifecycle action
                    self._log_lifecycle_action(auction_id, lot_id, 'bids_fetched', 'success', f"Found {len(bids)} bids")

                    return bids
                else:
                    logger.error(f"Failed to save bids for lot {lot_id}")
                    return []
            else:
                logger.warning(f"No bids found for lot {lot_id}")
                self._log_lifecycle_action(0, lot_id, 'bids_fetched', 'no_data', "No bids found")
                return []

        except Exception as e:
            logger.error(f"Error fetching bids for lot {lot_id}: {e}")
            self._log_lifecycle_action(0, lot_id, 'bids_fetched', 'error', str(e))
            return []

    def _log_lifecycle_action(self, auction_id: int, lot_id: int, action: str, status: str, details: str = ""):
        """Log lifecycle actions"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO lifecycle_log (auction_id, lot_id, action, status, timestamp, details)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (auction_id, lot_id, action, status, datetime.now().isoformat(), details))
                conn.commit()
        except Exception as e:
            logger.warning(f"Error logging lifecycle action: {e}")

def test_complete_lifecycle():
    """Test the complete lifecycle workflow"""
    print("🧪 TESTING COMPLETE LIFECYCLE WORKFLOW")
    print("=" * 60)

    crawler = LifecycleCrawler()

    # STEP 1: Fetch all auctions
    print("\n🔍 STEP 1: Fetching all auctions...")
    auctions = crawler.fetch_all_auctions()
    print(f"   ✅ Found {len(auctions)} auctions")

    if not auctions:
        print("❌ No auctions found, cannot continue")
        return False

    # STEP 2: Fetch lots for first auction
    test_auction = auctions[0]
    auction_id = test_auction['auction_id']
    print(f"\n🔍 STEP 2: Fetching lots for auction {auction_id}...")
    lots = crawler.fetch_auction_lots(auction_id)
    print(f"   ✅ Found {len(lots)} lots")

    if not lots:
        print("❌ No lots found, cannot continue")
        return False

    # STEP 3: Fetch bids for first lot
    test_lot = lots[0]
    lot_id = test_lot['lot_id']
    lot_title = test_lot.get('title', '')
    print(f"\n🔍 STEP 3: Fetching bids for lot {lot_id}...")
    bids = crawler.fetch_lot_bids(lot_id, lot_title)
    print(f"   ✅ Found {len(bids)} bids")

    # Show results
    print(f"\n📊 LIFECYCLE TEST RESULTS:")
    print(f"   Auctions: {len(auctions)}")
    print(f"   Lots: {len(lots)}")
    print(f"   Bids: {len(bids)}")

    if auctions and lots:
        print(f"\n📋 SAMPLE DATA:")
        print(f"   Auction: {test_auction['title'][:50]}...")
        print(f"   Lot: {test_lot['title'][:50]}...")
        if bids:
            print(f"   Latest bid: €{bids[0]['amount_euros']} on {bids[0]['bid_date']}")

    return len(auctions) > 0 and len(lots) > 0

if __name__ == "__main__":
    test_complete_lifecycle()
