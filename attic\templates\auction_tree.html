<!DOCTYPE html>
<html>
<head>
    <title>Auction Tree - <PERSON>rena Tracker</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .auction-node {
            border-left: 3px solid #dee2e6;
            margin-left: 10px;
            padding-left: 15px;
            margin-bottom: 20px;
        }
        .auction-node.active {
            border-left-color: #198754;
        }
        .auction-node.waiting {
            border-left-color: #6c757d;
        }
        .auction-node.closed {
            border-left-color: #dc3545;
        }
        .lot-list {
            max-height: 300px;
            overflow-y: auto;
        }
        .lot-item {
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            margin-bottom: 5px;
            border: 1px solid #e9ecef;
        }
        .lot-item:hover {
            background-color: #f8f9fa;
            border-color: #007bff;
        }
        .expand-btn {
            cursor: pointer;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-gavel me-2"></i>
                Aurena Auction Tracker
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Dashboard</a>
                <a class="nav-link" href="/timetable">Timetable</a>
                <a class="nav-link active" href="/auction-tree">Auction Tree</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-sitemap me-2"></i>Auction Tree Navigation</h2>
            <div class="btn-group" role="group">
                <button class="btn btn-outline-primary" onclick="expandAll()">Expand All</button>
                <button class="btn btn-outline-secondary" onclick="collapseAll()">Collapse All</button>
            </div>
        </div>

        <!-- Status Legend -->
        <div class="card mb-4">
            <div class="card-body">
                <h6>Auction Status:</h6>
                <div class="d-flex flex-wrap gap-3">
                    <span><span class="badge bg-success">Active</span> - Currently running</span>
                    <span><span class="badge bg-secondary">Waiting</span> - Not started yet</span>
                    <span><span class="badge bg-danger">Closed</span> - Auction ended</span>
                </div>
            </div>
        </div>

        <!-- Auction Tree -->
        <div class="row">
            {% if auction_tree %}
                {% for auction in auction_tree %}
                <div class="col-12 mb-4">
                    <div class="auction-node {{ auction.timetable_status.lower() }}">
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h5 class="mb-1">
                                            <i class="fas fa-gavel me-2"></i>
                                            <a href="/auction/{{ auction.auction_id }}" class="text-decoration-none">
                                                Auction {{ auction.auction_id }}
                                            </a>
                                        </h5>
                                        <h6 class="text-muted">{{ auction.title }}</h6>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-{{ 'success' if auction.timetable_status == 'Active' else 'secondary' }} fs-6">
                                            {{ auction.timetable_status }}
                                        </span>
                                        <div class="mt-1">
                                            <span class="expand-btn btn btn-sm btn-outline-primary" 
                                                  onclick="toggleLots({{ auction.auction_id }})" 
                                                  id="btn-{{ auction.auction_id }}">
                                                <i class="fas fa-chevron-down"></i> Show Lots
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-3">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            Start: {{ auction.start_time.strftime('%Y-%m-%d %H:%M') }}
                                        </small>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar-times me-1"></i>
                                            End: {{ auction.end_time.strftime('%Y-%m-%d %H:%M') }}
                                        </small>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">
                                            <i class="fas fa-box me-1"></i>
                                            {{ auction.lot_count }} lots
                                        </small>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">
                                            <i class="fas fa-euro-sign me-1"></i>
                                            {{ auction.lots_with_bids }} with bids
                                        </small>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body lot-list d-none" id="lots-{{ auction.auction_id }}">
                                <div class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading lots...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-sitemap fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No auctions found</h5>
                        <p class="text-muted">Check back later for auction data.</p>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        async function toggleLots(auctionId) {
            const lotsDiv = document.getElementById(`lots-${auctionId}`);
            const btn = document.getElementById(`btn-${auctionId}`);
            
            if (lotsDiv.classList.contains('d-none')) {
                // Show lots
                lotsDiv.classList.remove('d-none');
                btn.innerHTML = '<i class="fas fa-chevron-up"></i> Hide Lots';
                
                // Load lots if not already loaded
                if (lotsDiv.innerHTML.includes('spinner-border')) {
                    try {
                        const response = await fetch(`/api/lots/${auctionId}`);
                        const lots = await response.json();
                        
                        if (lots.length > 0) {
                            let lotsHtml = '';
                            lots.forEach(lot => {
                                const closingTime = new Date(lot.closing_time);
                                const now = new Date();
                                const timeLeft = closingTime - now;
                                
                                let statusClass = 'secondary';
                                let statusText = 'Unknown';
                                
                                if (timeLeft > 3600000) {
                                    statusClass = 'info';
                                    statusText = 'Waiting';
                                } else if (timeLeft > 300000) {
                                    statusClass = 'warning';
                                    statusText = 'Closing Soon';
                                } else if (timeLeft > 0) {
                                    statusClass = 'danger';
                                    statusText = 'Critical';
                                } else {
                                    statusClass = 'secondary';
                                    statusText = 'Closed';
                                }
                                
                                lotsHtml += `
                                    <div class="lot-item" onclick="window.location.href='/lot/${lot.lot_id}'">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">Lot ${lot.lot_number}: ${lot.title.substring(0, 40)}${lot.title.length > 40 ? '...' : ''}</h6>
                                                <small class="text-muted">${lot.description.substring(0, 60)}${lot.description.length > 60 ? '...' : ''}</small>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge bg-${statusClass}">${statusText}</span>
                                                <div class="text-success fw-bold">€${lot.current_price}</div>
                                                <small class="text-muted">${closingTime.toLocaleString()}</small>
                                            </div>
                                        </div>
                                    </div>
                                `;
                            });
                            lotsDiv.innerHTML = lotsHtml;
                        } else {
                            lotsDiv.innerHTML = '<p class="text-muted text-center">No lots found for this auction.</p>';
                        }
                    } catch (error) {
                        lotsDiv.innerHTML = '<p class="text-danger text-center">Error loading lots.</p>';
                    }
                }
            } else {
                // Hide lots
                lotsDiv.classList.add('d-none');
                btn.innerHTML = '<i class="fas fa-chevron-down"></i> Show Lots';
            }
        }
        
        function expandAll() {
            document.querySelectorAll('.lot-list').forEach(div => {
                if (div.classList.contains('d-none')) {
                    const auctionId = div.id.replace('lots-', '');
                    toggleLots(auctionId);
                }
            });
        }
        
        function collapseAll() {
            document.querySelectorAll('.lot-list').forEach(div => {
                if (!div.classList.contains('d-none')) {
                    const auctionId = div.id.replace('lots-', '');
                    toggleLots(auctionId);
                }
            });
        }
    </script>
</body>
</html>
