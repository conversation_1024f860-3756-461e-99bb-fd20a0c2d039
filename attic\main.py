#!/usr/bin/env python3
"""
Main script for Aurena auction crawler

Usage:
    python main.py                    # Run single crawl
    python main.py --timetable        # Show current timetable
    python main.py --export-csv       # Export timetable to CSV
    python main.py --history          # Show crawl history
"""
import argparse
import sys
from datetime import datetime

from aurena_crawler import AurenaCrawler
from data_storage import AuctionDataStorage


def run_crawl():
    """Run a single crawl of auction data"""
    print("Starting Aurena auction crawl...")

    crawler = AurenaCrawler()
    storage = AuctionDataStorage()

    try:
        # Crawl auctions
        auctions = crawler.crawl_auctions()

        if not auctions:
            print("No auctions found!")
            return False

        # Save to database
        success = storage.save_auctions(auctions)

        if success:
            print(f"Successfully crawled and saved {len(auctions)} auctions")
            return True
        else:
            print("Failed to save auction data")
            return False

    except Exception as e:
        print(f"Crawl failed: {e}")
        return False


def show_timetable():
    """Display current auction timetable"""
    storage = AuctionDataStorage()

    try:
        df = storage.get_auction_timetable_df()

        if df.empty:
            print("No auction data found. Run a crawl first.")
            return

        print("\n=== AURENA AUCTION TIMETABLE ===")
        print(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Total auctions: {len(df)}")
        print("-" * 80)

        for _, auction in df.iterrows():
            start_date = datetime.fromisoformat(auction['starting_date'])
            end_date = datetime.fromisoformat(auction['ending_date'])

            print(f"ID: {auction['auction_id']}")
            print(f"Title: {auction['title']}")
            print(f"Start: {start_date.strftime('%Y-%m-%d %H:%M')}")
            print(f"End: {end_date.strftime('%Y-%m-%d %H:%M')}")
            print(f"Location: {auction['location_city']}, {auction['location_state']}")
            print(f"Lots: {auction['lot_count']}")
            print(f"Type: {auction['auction_type']} | Status: {auction['status_id']}")
            print("-" * 40)

    except Exception as e:
        print(f"Failed to show timetable: {e}")


def export_csv():
    """Export timetable to CSV"""
    storage = AuctionDataStorage()

    try:
        filename = storage.export_timetable_csv()
        print(f"Timetable exported to: {filename}")

    except Exception as e:
        print(f"Failed to export CSV: {e}")


def show_history():
    """Show crawl history"""
    storage = AuctionDataStorage()

    try:
        df = storage.get_crawl_history()

        if df.empty:
            print("No crawl history found.")
            return

        print("\n=== CRAWL HISTORY ===")
        for _, record in df.iterrows():
            timestamp = datetime.fromisoformat(record['crawl_timestamp'])
            status = "SUCCESS" if record['success'] else "FAILED"

            print(f"{timestamp.strftime('%Y-%m-%d %H:%M:%S')} | {status} | {record['auctions_found']} auctions")
            if record['error_message']:
                print(f"  Error: {record['error_message']}")

    except Exception as e:
        print(f"Failed to show history: {e}")


def main():
    parser = argparse.ArgumentParser(description='Aurena Auction Crawler')
    parser.add_argument('--timetable', action='store_true', help='Show current auction timetable')
    parser.add_argument('--export-csv', action='store_true', help='Export timetable to CSV')
    parser.add_argument('--history', action='store_true', help='Show crawl history')

    args = parser.parse_args()

    if args.timetable:
        show_timetable()
    elif args.export_csv:
        export_csv()
    elif args.history:
        show_history()
    else:
        # Default action: run crawl
        success = run_crawl()
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()