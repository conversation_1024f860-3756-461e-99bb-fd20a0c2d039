{% extends "base.html" %}

{% block title %}Auction Tree - Auction Scraper{% endblock %}

{% block content %}
<div class="col-12">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-diagram-3"></i> Auction Tree Navigation</h1>
        <button class="btn btn-primary" onclick="triggerAuctionDiscovery()">
            <i class="bi bi-search"></i> Discover More Auctions
        </button>
    </div>

    {% if tree_data %}
        <div class="card">
            <div class="card-body">
                <div class="tree-container">
                    {% for auction in tree_data %}
                    <div class="card mb-3 auction-node">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <button class="btn btn-sm btn-outline-secondary me-2" type="button" 
                                        data-bs-toggle="collapse" data-bs-target="#auction-{{ auction.auction_id }}"
                                        onclick="toggleAuctionNode({{ auction.auction_id }})">
                                    <i class="bi bi-chevron-right" id="chevron-{{ auction.auction_id }}"></i>
                                </button>
                                <i class="bi bi-folder me-2"></i>
                                <strong>{{ auction.title }}</strong>
                                <span class="badge bg-secondary ms-2">ID: {{ auction.auction_id }}</span>
                            </div>
                            <div>
                                <span class="badge bg-info me-2">{{ auction.actual_lots }} lots</span>
                                <span class="badge {{ auction.status | status_badge }}">{{ auction.status }}</span>
                                <div class="btn-group btn-group-sm ms-2">
                                    <a href="{{ url_for('auction_details', auction_id=auction.auction_id) }}" 
                                       class="btn btn-outline-primary">
                                        <i class="bi bi-eye"></i> View
                                    </a>
                                    <button class="btn btn-outline-success" onclick="triggerLotDiscovery({{ auction.auction_id }})">
                                        <i class="bi bi-search"></i> Lots
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="collapse" id="auction-{{ auction.auction_id }}">
                            <div class="card-body">
                                <div class="lot-loading text-center py-3" id="loading-{{ auction.auction_id }}">
                                    <div class="spinner-border spinner-border-sm" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <span class="ms-2">Loading lots...</span>
                                </div>
                                <div class="lot-container" id="lots-{{ auction.auction_id }}" style="display: none;">
                                    <!-- Lots will be loaded here dynamically -->
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    {% else %}
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="bi bi-tree fs-1 text-muted"></i>
                <h3 class="mt-3">No Auction Tree Data</h3>
                <p class="text-muted">No auctions have been discovered yet. Start by discovering auctions.</p>
                <button class="btn btn-primary" onclick="triggerAuctionDiscovery()">
                    <i class="bi bi-search"></i> Discover Auctions
                </button>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_css %}
<style>
    .auction-node {
        border-left: 4px solid #007bff;
    }
    .lot-item {
        border-left: 2px solid #28a745;
        margin-left: 20px;
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 4px;
        margin-bottom: 5px;
    }
    .lot-item:hover {
        background-color: #e9ecef;
    }
    .tree-container {
        max-height: 80vh;
        overflow-y: auto;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    let loadedAuctions = new Set();
    
    async function toggleAuctionNode(auctionId) {
        const chevron = document.getElementById(`chevron-${auctionId}`);
        const isExpanded = chevron.classList.contains('bi-chevron-down');
        
        if (isExpanded) {
            // Collapse
            chevron.classList.remove('bi-chevron-down');
            chevron.classList.add('bi-chevron-right');
        } else {
            // Expand
            chevron.classList.remove('bi-chevron-right');
            chevron.classList.add('bi-chevron-down');
            
            // Load lots if not already loaded
            if (!loadedAuctions.has(auctionId)) {
                await loadLotsForAuction(auctionId);
                loadedAuctions.add(auctionId);
            }
        }
    }
    
    async function loadLotsForAuction(auctionId) {
        const loadingDiv = document.getElementById(`loading-${auctionId}`);
        const lotsContainer = document.getElementById(`lots-${auctionId}`);
        
        try {
            loadingDiv.style.display = 'block';
            lotsContainer.style.display = 'none';
            
            // Fetch lots for this auction
            const response = await fetch(`/api/auction/${auctionId}/lots`);
            
            if (response.ok) {
                const lots = await response.json();
                renderLots(auctionId, lots);
            } else {
                // If API doesn't exist, show placeholder
                renderLotsPlaceholder(auctionId);
            }
        } catch (error) {
            console.error('Error loading lots:', error);
            renderLotsPlaceholder(auctionId);
        } finally {
            loadingDiv.style.display = 'none';
            lotsContainer.style.display = 'block';
        }
    }
    
    function renderLots(auctionId, lots) {
        const container = document.getElementById(`lots-${auctionId}`);
        
        if (lots && lots.length > 0) {
            let html = '<div class="row">';
            
            lots.forEach(lot => {
                const closingTime = lot.closing_time ? new Date(lot.closing_time).toLocaleString() : 'TBD';
                const currentPrice = lot.current_price ? `€${lot.current_price.toLocaleString()}` : '€0';
                
                html += `
                    <div class="col-md-6 mb-2">
                        <div class="lot-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-1">
                                        <i class="bi bi-box me-2"></i>
                                        <strong>Lot ${lot.lot_number || lot.lot_id}</strong>
                                        <span class="badge bg-secondary ms-2">${lot.status || 'active'}</span>
                                    </div>
                                    <div class="text-truncate mb-1" style="max-width: 200px;">
                                        <a href="/lot/${lot.lot_id}" class="text-decoration-none">
                                            ${lot.title || 'Untitled Lot'}
                                        </a>
                                    </div>
                                    <small class="text-muted">
                                        Price: ${currentPrice} | Closes: ${closingTime}
                                    </small>
                                </div>
                                <div class="btn-group-vertical btn-group-sm">
                                    <a href="/lot/${lot.lot_id}" class="btn btn-outline-info btn-sm">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <button class="btn btn-outline-primary btn-sm" onclick="triggerBidCollection(${lot.lot_id})">
                                        <i class="bi bi-currency-euro"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            container.innerHTML = html;
        } else {
            renderLotsPlaceholder(auctionId);
        }
    }
    
    function renderLotsPlaceholder(auctionId) {
        const container = document.getElementById(`lots-${auctionId}`);
        container.innerHTML = `
            <div class="text-center text-muted py-3">
                <i class="bi bi-box"></i>
                <span class="ms-2">No lots found for this auction</span>
                <button class="btn btn-sm btn-outline-primary ms-2" onclick="triggerLotDiscovery(${auctionId})">
                    <i class="bi bi-search"></i> Discover Lots
                </button>
            </div>
        `;
    }
    
    // Auto-expand first auction if there are few auctions
    document.addEventListener('DOMContentLoaded', function() {
        const auctionNodes = document.querySelectorAll('.auction-node');
        if (auctionNodes.length <= 3 && auctionNodes.length > 0) {
            // Auto-expand first auction
            const firstAuctionId = auctionNodes[0].querySelector('[data-bs-target]')
                .getAttribute('data-bs-target').replace('#auction-', '');
            
            setTimeout(() => {
                const collapseElement = document.getElementById(`auction-${firstAuctionId}`);
                if (collapseElement) {
                    new bootstrap.Collapse(collapseElement, { show: true });
                    toggleAuctionNode(parseInt(firstAuctionId));
                }
            }, 500);
        }
    });
</script>
{% endblock %}
