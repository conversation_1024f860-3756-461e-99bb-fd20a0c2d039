#!/usr/bin/env python3
"""
Main Aurena Auction Crawler Script
Combines auction crawling and web interface in a streamlined solution.
"""

import argparse
import sys
import threading
import time
from auction_crawler import AuctionCrawler
from web_interface import app

def run_crawler_once():
    """Run the crawler once to fetch all auctions and lots"""
    print("🚀 Starting Aurena Auction Crawler...")
    
    crawler = AuctionCrawler()
    
    # Test with specific auction first
    print("📋 Testing with auction 14941...")
    lots = crawler.extract_lots_from_auction(14941)
    
    if lots:
        print(f"✅ Successfully extracted {len(lots)} lots from auction 14941")
        
        # Find Van Ford Galaxy
        ford_lot = None
        for lot in lots:
            if 'Van Ford Galaxy' in lot.title:
                ford_lot = lot
                break
        
        if ford_lot:
            print(f"\n🚗 VAN FORD GALAXY LOT FOUND:")
            print(f"   Lot ID: {ford_lot.lot_id}")
            print(f"   Title: {ford_lot.title}")
            print(f"   Current Price: €{ford_lot.current_price}")
            print(f"   Closing Time: {ford_lot.closing_time}")
            print(f"   Images: {len(ford_lot.images)} found")
        
        # Save to database
        print("💾 Saving lots to database...")
        if crawler.save_lots(lots):
            print("✅ Lots saved successfully")
        else:
            print("❌ Failed to save lots")
        
        # Show database stats
        stats = crawler.get_database_stats()
        print(f"\n📊 Database Statistics:")
        print(f"   Total Auctions: {stats.get('total_auctions', 0)}")
        print(f"   Total Lots: {stats.get('total_lots', 0)}")
        print(f"   Lots with Bids: {stats.get('lots_with_bids', 0)}")
        print(f"   Upcoming Lots: {stats.get('upcoming_lots', 0)}")
        
        return True
    else:
        print("❌ No lots extracted")
        return False

def run_full_crawl():
    """Run full crawl of all auctions"""
    print("🌐 Starting full auction crawl...")
    
    crawler = AuctionCrawler()
    success = crawler.crawl_all_auctions()
    
    if success:
        print("✅ Full crawl completed successfully!")
        return True
    else:
        print("❌ Full crawl failed")
        return False

def run_web_interface():
    """Run the web interface"""
    print("🌐 Starting web interface...")
    print("📱 Access the dashboard at: http://localhost:5000")
    app.run(debug=False, host='0.0.0.0', port=5000)

def run_crawler_daemon():
    """Run crawler in daemon mode (periodic crawling)"""
    print("🔄 Starting crawler daemon mode...")
    print("⏰ Will crawl every 30 minutes")
    
    crawler = AuctionCrawler()
    
    while True:
        try:
            print(f"\n🕐 {time.strftime('%Y-%m-%d %H:%M:%S')} - Starting crawl cycle...")
            success = crawler.crawl_all_auctions()
            
            if success:
                print("✅ Crawl cycle completed successfully")
            else:
                print("❌ Crawl cycle failed")
            
            print("😴 Sleeping for 30 minutes...")
            time.sleep(1800)  # 30 minutes
            
        except KeyboardInterrupt:
            print("\n🛑 Crawler daemon stopped by user")
            break
        except Exception as e:
            print(f"❌ Error in crawler daemon: {e}")
            print("😴 Sleeping for 5 minutes before retry...")
            time.sleep(300)  # 5 minutes

def run_combined():
    """Run both crawler daemon and web interface"""
    print("🚀 Starting combined mode: Crawler + Web Interface")
    
    # Start crawler daemon in background thread
    crawler_thread = threading.Thread(target=run_crawler_daemon, daemon=True)
    crawler_thread.start()
    
    # Start web interface in main thread
    time.sleep(2)  # Give crawler a moment to start
    run_web_interface()

def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(description='Aurena Auction Crawler')
    parser.add_argument('mode', choices=['test', 'crawl', 'web', 'daemon', 'combined'], 
                       help='Operation mode')
    parser.add_argument('--port', type=int, default=5000, 
                       help='Port for web interface (default: 5000)')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🎯 AURENA AUCTION CRAWLER")
    print("=" * 60)
    
    try:
        if args.mode == 'test':
            print("🧪 Test Mode: Extract lots from auction 14941")
            success = run_crawler_once()
            sys.exit(0 if success else 1)
            
        elif args.mode == 'crawl':
            print("🕷️ Crawl Mode: Full auction crawl")
            success = run_full_crawl()
            sys.exit(0 if success else 1)
            
        elif args.mode == 'web':
            print("🌐 Web Mode: Start web interface only")
            # Update port if specified
            if args.port != 5000:
                print(f"📱 Access the dashboard at: http://localhost:{args.port}")
                app.run(debug=False, host='0.0.0.0', port=args.port)
            else:
                run_web_interface()
                
        elif args.mode == 'daemon':
            print("🔄 Daemon Mode: Periodic crawling")
            run_crawler_daemon()
            
        elif args.mode == 'combined':
            print("🚀 Combined Mode: Crawler + Web Interface")
            # Update port if specified
            if args.port != 5000:
                print(f"📱 Access the dashboard at: http://localhost:{args.port}")
                # Start crawler daemon in background thread
                crawler_thread = threading.Thread(target=run_crawler_daemon, daemon=True)
                crawler_thread.start()
                time.sleep(2)
                app.run(debug=False, host='0.0.0.0', port=args.port)
            else:
                run_combined()
                
    except KeyboardInterrupt:
        print("\n🛑 Stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
