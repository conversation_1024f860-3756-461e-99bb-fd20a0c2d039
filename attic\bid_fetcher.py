#!/usr/bin/env python3
"""
Bid History Fetcher for Aurena Auctions
Extracts real bid history data from individual lot pages.
"""

import requests
from bs4 import BeautifulSoup
import re
import sqlite3
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BidFetcher:
    """Fetches real bid history from Aurena lot pages"""
    
    def __init__(self, db_path: str = "aurena_auctions.db"):
        self.db_path = db_path
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.init_bid_tables()
    
    def init_bid_tables(self):
        """Initialize bid-related database tables"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Create enhanced bids table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS real_bids (
                    bid_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    lot_id INTEGER NOT NULL,
                    auction_id INTEGER NOT NULL,
                    amount INTEGER NOT NULL,
                    bid_date TEXT NOT NULL,
                    bid_time TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    fetched_at TEXT NOT NULL,
                    FOREIGN KEY (lot_id) REFERENCES lots (lot_id),
                    FOREIGN KEY (auction_id) REFERENCES auctions (auction_id)
                )
            """)
            
            # Create lot monitoring table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS lot_monitoring (
                    lot_id INTEGER PRIMARY KEY,
                    auction_id INTEGER NOT NULL,
                    closing_time TEXT NOT NULL,
                    last_checked TEXT,
                    last_bid_count INTEGER DEFAULT 0,
                    current_price INTEGER DEFAULT 0,
                    status TEXT DEFAULT 'active',
                    monitor_start TEXT,
                    monitor_end TEXT,
                    FOREIGN KEY (lot_id) REFERENCES lots (lot_id)
                )
            """)
            
            conn.commit()
            logger.info("Bid tables initialized successfully")
    
    def fetch_lot_bid_history(self, lot_id: int, lot_title: str = "") -> List[Dict]:
        """
        Fetch bid history for a specific lot from its Aurena page
        
        Args:
            lot_id: The lot ID to fetch bids for
            lot_title: Optional lot title for URL construction
            
        Returns:
            List of bid dictionaries with amount, date, time, timestamp
        """
        # Construct lot URL - we need to handle the title part
        if lot_title:
            # Clean title for URL
            clean_title = re.sub(r'[^a-zA-Z0-9_]', '_', lot_title)
            clean_title = re.sub(r'_+', '_', clean_title).strip('_')
            lot_url = f'https://www.aurena.at/posten/{lot_id}/{clean_title}'
        else:
            # Try common patterns or just use lot ID
            lot_url = f'https://www.aurena.at/posten/{lot_id}/'
        
        logger.info(f"Fetching bid history for lot {lot_id} from {lot_url}")
        
        try:
            response = self.session.get(lot_url, timeout=30)
            
            # If first URL fails, try alternative patterns
            if response.status_code == 404 and lot_title:
                # Try with different title cleaning
                alt_title = lot_title.replace(' ', '_').replace('/', '_')
                alt_url = f'https://www.aurena.at/posten/{lot_id}/{alt_title}'
                response = self.session.get(alt_url, timeout=30)
                
                if response.status_code == 404:
                    # Try just the lot ID
                    simple_url = f'https://www.aurena.at/posten/{lot_id}/'
                    response = self.session.get(simple_url, timeout=30)
            
            if response.status_code != 200:
                logger.warning(f"Failed to fetch lot page {lot_id}: HTTP {response.status_code}")
                return []
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find the bid history list
            bid_history_list = soup.find('div', class_='bidhistorylist')
            
            if not bid_history_list:
                logger.warning(f"No bid history found for lot {lot_id}")
                return []
            
            bid_items = bid_history_list.find_all('div', class_='bidhistoryitem')
            logger.info(f"Found {len(bid_items)} bid history items for lot {lot_id}")
            
            bids = []
            for item in bid_items:
                try:
                    # Extract bid amount
                    amount_span = item.find('span', class_='bidhistoryitemleft')
                    amount_text = amount_span.text.strip() if amount_span else ''
                    
                    # Parse amount (remove € and convert to cents)
                    amount_match = re.search(r'€\s*(\d+)', amount_text)
                    if not amount_match:
                        continue
                    amount = int(amount_match.group(1)) * 100  # Convert to cents
                    
                    # Extract bid date
                    date_span = item.find('span', class_='bidhistoryitemmiddle')
                    bid_date = date_span.text.strip() if date_span else ''
                    
                    # Extract bid time
                    time_span = item.find('span', class_='bidhistoryitemright')
                    bid_time = time_span.text.strip() if time_span else ''
                    
                    # Create full timestamp
                    if bid_date and bid_time:
                        try:
                            # Parse date format like "28.08.25"
                            date_parts = bid_date.split('.')
                            if len(date_parts) == 3:
                                day, month, year = date_parts
                                # Handle 2-digit year
                                if len(year) == 2:
                                    year = '20' + year
                                
                                # Create datetime
                                timestamp_str = f"{year}-{month.zfill(2)}-{day.zfill(2)} {bid_time}:00"
                                timestamp = datetime.fromisoformat(timestamp_str)
                            else:
                                timestamp = datetime.now()
                        except:
                            timestamp = datetime.now()
                    else:
                        timestamp = datetime.now()
                    
                    bid_data = {
                        'amount': amount,
                        'bid_date': bid_date,
                        'bid_time': bid_time,
                        'timestamp': timestamp,
                        'amount_euros': amount // 100
                    }
                    
                    bids.append(bid_data)
                    
                except Exception as e:
                    logger.warning(f"Error parsing bid item for lot {lot_id}: {e}")
                    continue
            
            # Sort bids by timestamp (newest first)
            bids.sort(key=lambda x: x['timestamp'], reverse=True)
            
            logger.info(f"Successfully parsed {len(bids)} bids for lot {lot_id}")
            return bids
            
        except Exception as e:
            logger.error(f"Error fetching bid history for lot {lot_id}: {e}")
            return []
    
    def save_bid_history(self, lot_id: int, auction_id: int, bids: List[Dict]) -> bool:
        """Save bid history to database"""
        if not bids:
            return True
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                current_time = datetime.now().isoformat()
                
                # Clear existing bids for this lot
                cursor.execute("DELETE FROM real_bids WHERE lot_id = ?", (lot_id,))
                
                # Insert new bids
                for bid in bids:
                    cursor.execute("""
                        INSERT INTO real_bids (
                            lot_id, auction_id, amount, bid_date, bid_time, 
                            timestamp, fetched_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        lot_id, auction_id, bid['amount'], bid['bid_date'],
                        bid['bid_time'], bid['timestamp'].isoformat(), current_time
                    ))
                
                conn.commit()
                logger.info(f"Saved {len(bids)} bids for lot {lot_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error saving bid history for lot {lot_id}: {e}")
            return False
    
    def fetch_and_save_lot_bids(self, lot_id: int, auction_id: int, lot_title: str = "") -> bool:
        """Fetch and save bid history for a lot"""
        bids = self.fetch_lot_bid_history(lot_id, lot_title)
        if bids:
            return self.save_bid_history(lot_id, auction_id, bids)
        return False
    
    def get_lots_needing_monitoring(self, hours_before: int = 2, hours_after: int = 1) -> List[Dict]:
        """Get lots that need bid monitoring (closing soon or recently closed)"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                now = datetime.now()
                monitor_start = (now - timedelta(hours=hours_after)).isoformat()
                monitor_end = (now + timedelta(hours=hours_before)).isoformat()
                
                cursor.execute("""
                    SELECT l.lot_id, l.auction_id, l.title, l.closing_time, l.current_price
                    FROM lots l
                    WHERE l.closing_time BETWEEN ? AND ?
                    AND l.status = 'active'
                    ORDER BY l.closing_time
                """, (monitor_start, monitor_end))
                
                lots = []
                for row in cursor.fetchall():
                    lot = dict(row)
                    lot['closing_time'] = datetime.fromisoformat(lot['closing_time'])
                    lots.append(lot)
                
                return lots
                
        except Exception as e:
            logger.error(f"Error getting lots for monitoring: {e}")
            return []

def test_bid_fetcher():
    """Test the bid fetcher with known lot"""
    fetcher = BidFetcher()
    
    # Test with the lot we know has bid history
    lot_id = 3532311
    lot_title = "6_0Palettenregalrahmen_0"
    auction_id = 14941
    
    print(f"Testing bid fetcher with lot {lot_id}")
    
    # Fetch bid history
    bids = fetcher.fetch_lot_bid_history(lot_id, lot_title)
    
    if bids:
        print(f"✅ Successfully fetched {len(bids)} bids:")
        for i, bid in enumerate(bids):
            print(f"  {i+1}. €{bid['amount_euros']} on {bid['bid_date']} at {bid['bid_time']}")
        
        # Save to database
        if fetcher.save_bid_history(lot_id, auction_id, bids):
            print("✅ Successfully saved bids to database")
        else:
            print("❌ Failed to save bids to database")
    else:
        print("❌ No bids fetched")

if __name__ == "__main__":
    test_bid_fetcher()
