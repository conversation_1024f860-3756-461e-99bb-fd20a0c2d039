<!DOCTYPE html>
<html>
<head>
    <title>Lot {{ lot.lot_number }} - {{ lot.title[:50] }} - <PERSON><PERSON> Tracker</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .lot-image {
            max-width: 100%;
            height: 300px;
            object-fit: cover;
            border-radius: 8px;
        }
        .bid-history {
            max-height: 400px;
            overflow-y: auto;
        }
        .current-price {
            font-size: 2rem;
            font-weight: bold;
        }
        .countdown {
            font-family: 'Courier New', monospace;
            font-size: 1.5rem;
            font-weight: bold;
        }
        .status-critical {
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-gavel me-2"></i>
                Aurena Auction Tracker
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Dashboard</a>
                <a class="nav-link" href="/timetable">Timetable</a>
                <a class="nav-link" href="/auction-tree">Auction Tree</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="/auction/{{ lot.auction_id }}">Auction {{ lot.auction_id }}</a></li>
                <li class="breadcrumb-item active">Lot {{ lot.lot_number }}</li>
            </ol>
        </nav>

        <div class="row">
            <!-- Lot Information -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h4><i class="fas fa-box me-2"></i>Lot {{ lot.lot_number }}: {{ lot.title }}</h4>
                        <p class="text-muted mb-0">
                            <i class="fas fa-gavel me-1"></i>
                            <a href="/auction/{{ lot.auction_id }}">{{ lot.auction_title }}</a>
                        </p>
                    </div>
                    <div class="card-body">
                        {% if lot.images %}
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <img src="{{ lot.images[0] }}" alt="Lot Image" class="lot-image">
                            </div>
                            <div class="col-md-6">
                                {% if lot.images|length > 1 %}
                                <div class="row">
                                    {% for image in lot.images[1:5] %}
                                    <div class="col-6 mb-2">
                                        <img src="{{ image }}" alt="Lot Image" class="img-thumbnail" style="height: 100px; object-fit: cover;">
                                    </div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}

                        <div class="row">
                            <div class="col-md-8">
                                <h6>Description:</h6>
                                <p>{{ lot.description or 'No description available.' }}</p>
                                
                                {% if lot.category %}
                                <p><strong>Category:</strong> {{ lot.category }}</p>
                                {% endif %}
                                
                                {% if lot.condition %}
                                <p><strong>Condition:</strong> {{ lot.condition }}</p>
                                {% endif %}
                                
                                {% if lot.location %}
                                <p><strong>Location:</strong> {{ lot.location }}</p>
                                {% endif %}
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6>Lot Details</h6>
                                        <p><strong>Lot ID:</strong> {{ lot.lot_id }}</p>
                                        <p><strong>Starting Price:</strong> €{{ lot.starting_price }}</p>
                                        <p><strong>Status:</strong> 
                                            <span class="badge bg-{{ 'success' if lot.status == 'active' else 'secondary' }}">
                                                {{ lot.status.title() }}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bidding Information -->
            <div class="col-lg-4">
                <!-- Current Price & Countdown -->
                <div class="card mb-4">
                    <div class="card-header text-center">
                        <h5><i class="fas fa-euro-sign me-2"></i>Current Offer</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="current-price text-success">€{{ lot.current_price }}</div>
                        <p class="text-muted">Highest Bid</p>
                        
                        <hr>
                        
                        <h6>Time Remaining:</h6>
                        <div class="countdown text-primary" data-closing-time="{{ lot.closing_time.isoformat() }}">
                            Calculating...
                        </div>
                        <p class="text-muted small">
                            Closes: {{ lot.closing_time.strftime('%Y-%m-%d %H:%M:%S') }}
                        </p>
                    </div>
                </div>

                <!-- Bid History -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6><i class="fas fa-history me-2"></i>Bid History</h6>
                        <button class="btn btn-sm btn-outline-primary" onclick="fetchFreshBids({{ lot.lot_id }})">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                    </div>
                    <div class="card-body bid-history">
                        {% if bids %}
                            {% for bid in bids %}
                            <div class="d-flex justify-content-between align-items-center mb-2 p-2 border-bottom">
                                <div>
                                    <strong>€{{ bid.amount_euros }}</strong>
                                    {% if bid.is_real %}
                                    <span class="badge bg-success ms-1">Real</span>
                                    {% endif %}
                                    <br>
                                    {% if bid.is_real %}
                                    <small class="text-muted">{{ bid.bid_date }} {{ bid.bid_time }}</small>
                                    {% else %}
                                    <small class="text-muted">Bidder {{ bid.bidder_id or 'Unknown' }}</small>
                                    {% endif %}
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">
                                        {% if bid.is_real %}
                                            {{ bid.timestamp.strftime('%Y-%m-%d %H:%M:%S') if bid.timestamp else 'Unknown' }}
                                        {% else %}
                                            {{ bid.timestamp.strftime('%H:%M:%S') if bid.timestamp else 'Unknown' }}
                                        {% endif %}
                                    </small>
                                    <br>
                                    {% if bid.is_real %}
                                    <span class="badge bg-primary">Verified</span>
                                    {% else %}
                                    <span class="badge bg-{{ 'success' if bid.status == 'active' else 'secondary' }}">
                                        {{ bid.status.title() if bid.status else 'Unknown' }}
                                    </span>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}

                            {% if bids and bids[0].is_real %}
                            <div class="text-center mt-3">
                                <small class="text-muted">
                                    <i class="fas fa-check-circle text-success me-1"></i>
                                    Real bid data fetched from Aurena
                                    <br>
                                    Last updated: {{ bids[0].fetched_at.strftime('%Y-%m-%d %H:%M:%S') if bids[0].fetched_at else 'Unknown' }}
                                </small>
                            </div>
                            {% endif %}
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-history fa-2x text-muted mb-2"></i>
                                <p class="text-muted">No bid history available</p>
                                {% if lot.current_price > 0 %}
                                <p class="small text-muted">Current price: €{{ lot.current_price }}</p>
                                {% else %}
                                <p class="small text-muted">No bids placed yet</p>
                                {% endif %}
                                <button class="btn btn-primary mt-2" onclick="fetchFreshBids({{ lot.lot_id }})">
                                    <i class="fas fa-download me-1"></i>Fetch Bid History
                                </button>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <a href="/auction/{{ lot.auction_id }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Auction
                </a>
                <a href="/timetable" class="btn btn-outline-info">
                    <i class="fas fa-clock me-2"></i>View Timetable
                </a>
                <button class="btn btn-primary" onclick="window.open('https://www.aurena.at/auktion/{{ lot.auction_id }}', '_blank')">
                    <i class="fas fa-external-link-alt me-2"></i>View on Aurena
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Update countdown timer
        function updateCountdown() {
            const element = document.querySelector('[data-closing-time]');
            if (!element) return;
            
            const closingTime = new Date(element.dataset.closingTime);
            const now = new Date();
            const diff = closingTime - now;
            
            if (diff > 0) {
                const days = Math.floor(diff / (1000 * 60 * 60 * 24));
                const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((diff % (1000 * 60)) / 1000);
                
                let countdown = '';
                if (days > 0) {
                    countdown = `${days}d ${hours}h ${minutes}m ${seconds}s`;
                } else if (hours > 0) {
                    countdown = `${hours}h ${minutes}m ${seconds}s`;
                } else if (minutes > 0) {
                    countdown = `${minutes}m ${seconds}s`;
                } else {
                    countdown = `${seconds}s`;
                }
                
                element.textContent = countdown;
                
                // Update styling based on time remaining
                if (diff < 300000) { // Less than 5 minutes
                    element.className = 'countdown text-danger status-critical';
                } else if (diff < 3600000) { // Less than 1 hour
                    element.className = 'countdown text-warning';
                } else {
                    element.className = 'countdown text-primary';
                }
            } else {
                element.textContent = 'AUCTION CLOSED';
                element.className = 'countdown text-secondary';
            }
        }
        
        // Update countdown every second
        setInterval(updateCountdown, 1000);
        updateCountdown(); // Initial call
        
        // Auto-refresh every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);

        // Function to fetch fresh bid data
        async function fetchFreshBids(lotId) {
            const button = document.querySelector('button[onclick*="fetchFreshBids"]');
            const originalText = button.innerHTML;

            // Show loading state
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Fetching...';
            button.disabled = true;

            try {
                const response = await fetch(`/fetch-bids/${lotId}`);
                const result = await response.json();

                if (result.success) {
                    // Show success message
                    button.innerHTML = '<i class="fas fa-check me-1"></i>Updated!';
                    button.className = 'btn btn-sm btn-success';

                    // Reload page after short delay to show updated bids
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    // Show appropriate error message
                    const message = result.message || 'Unknown error';

                    if (message.includes('410') || message.includes('removed') || message.includes('closed')) {
                        button.innerHTML = '<i class="fas fa-info-circle me-1"></i>Closed';
                        button.className = 'btn btn-sm btn-warning';

                        // Show tooltip or alert with explanation
                        button.title = 'This lot has been closed and removed from Aurena. This is normal behavior.';
                    } else {
                        button.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>Error';
                        button.className = 'btn btn-sm btn-danger';
                        button.title = message;
                    }

                    console.log('Bid fetch result:', message);

                    // Reset button after delay
                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.className = 'btn btn-sm btn-outline-primary';
                        button.disabled = false;
                        button.title = '';
                    }, 5000);
                }
            } catch (error) {
                console.error('Error fetching bids:', error);
                button.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>Error';
                button.className = 'btn btn-sm btn-danger';

                // Reset button after delay
                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.className = 'btn btn-sm btn-outline-primary';
                    button.disabled = false;
                }, 3000);
            }
        }
    </script>
</body>
</html>
