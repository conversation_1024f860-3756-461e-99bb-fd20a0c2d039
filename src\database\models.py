#!/usr/bin/env python3
"""
Database Models for Auction Scraper System
Defines data structures for auctions, lots, and bids with proper relationships.
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List
import sqlite3
import json


@dataclass
class Auction:
    """Auction data model"""
    auction_id: int
    title: str
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    lot_count: int = 0
    location: str = ""
    status: str = "active"
    lifecycle_stage: str = "discovered"
    description: str = ""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
    
    def to_dict(self) -> dict:
        """Convert to dictionary for JSON serialization"""
        return {
            'auction_id': self.auction_id,
            'title': self.title,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'lot_count': self.lot_count,
            'location': self.location,
            'status': self.status,
            'lifecycle_stage': self.lifecycle_stage,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


@dataclass
class Lot:
    """Lot data model"""
    lot_id: int
    auction_id: int
    lot_number: Optional[int] = None
    title: str = ""
    description: str = ""
    starting_price: int = 0
    current_price: int = 0
    closing_time: Optional[datetime] = None
    status: str = "active"
    bid_count: int = 0
    images: List[str] = None
    lifecycle_stage: str = "discovered"
    bids_fetched: bool = False
    location: str = ""
    condition: str = ""
    category: str = ""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.images is None:
            self.images = []
        if self.lot_number is None:
            self.lot_number = self.lot_id
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
    
    def to_dict(self) -> dict:
        """Convert to dictionary for JSON serialization"""
        return {
            'lot_id': self.lot_id,
            'auction_id': self.auction_id,
            'lot_number': self.lot_number,
            'title': self.title,
            'description': self.description,
            'starting_price': self.starting_price,
            'current_price': self.current_price,
            'closing_time': self.closing_time.isoformat() if self.closing_time else None,
            'status': self.status,
            'bid_count': self.bid_count,
            'images': self.images,
            'lifecycle_stage': self.lifecycle_stage,
            'bids_fetched': self.bids_fetched,
            'location': self.location,
            'condition': self.condition,
            'category': self.category,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


@dataclass
class Bid:
    """Bid data model"""
    bid_id: Optional[int] = None
    lot_id: int = 0
    auction_id: int = 0
    amount: int = 0
    bid_date: str = ""
    bid_time: str = ""
    timestamp: Optional[datetime] = None
    fetched_at: Optional[datetime] = None
    bidder_id: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.fetched_at is None:
            self.fetched_at = datetime.now()
        if not self.bid_date and self.timestamp:
            self.bid_date = self.timestamp.strftime('%Y-%m-%d')
        if not self.bid_time and self.timestamp:
            self.bid_time = self.timestamp.strftime('%H:%M:%S')
    
    def to_dict(self) -> dict:
        """Convert to dictionary for JSON serialization"""
        return {
            'bid_id': self.bid_id,
            'lot_id': self.lot_id,
            'auction_id': self.auction_id,
            'amount': self.amount,
            'bid_date': self.bid_date,
            'bid_time': self.bid_time,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'fetched_at': self.fetched_at.isoformat() if self.fetched_at else None,
            'bidder_id': self.bidder_id
        }


@dataclass
class LifecycleLog:
    """Lifecycle log entry model"""
    log_id: Optional[int] = None
    auction_id: Optional[int] = None
    lot_id: Optional[int] = None
    action: str = ""
    status: str = ""
    timestamp: Optional[datetime] = None
    details: str = ""
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
    
    def to_dict(self) -> dict:
        """Convert to dictionary for JSON serialization"""
        return {
            'log_id': self.log_id,
            'auction_id': self.auction_id,
            'lot_id': self.lot_id,
            'action': self.action,
            'status': self.status,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'details': self.details
        }


class DatabaseSchema:
    """Database schema definitions"""
    
    CREATE_AUCTIONS_TABLE = """
    CREATE TABLE IF NOT EXISTS auctions (
        auction_id INTEGER PRIMARY KEY,
        title TEXT NOT NULL,
        start_time TEXT,
        end_time TEXT,
        lot_count INTEGER DEFAULT 0,
        location TEXT DEFAULT '',
        status TEXT DEFAULT 'active',
        lifecycle_stage TEXT DEFAULT 'discovered',
        description TEXT DEFAULT '',
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
    )
    """
    
    CREATE_LOTS_TABLE = """
    CREATE TABLE IF NOT EXISTS lots (
        lot_id INTEGER PRIMARY KEY,
        auction_id INTEGER NOT NULL,
        lot_number INTEGER,
        title TEXT NOT NULL DEFAULT '',
        description TEXT DEFAULT '',
        starting_price INTEGER DEFAULT 0,
        current_price INTEGER DEFAULT 0,
        closing_time TEXT,
        status TEXT DEFAULT 'active',
        bid_count INTEGER DEFAULT 0,
        images TEXT DEFAULT '[]',
        lifecycle_stage TEXT DEFAULT 'discovered',
        bids_fetched BOOLEAN DEFAULT FALSE,
        location TEXT DEFAULT '',
        condition TEXT DEFAULT '',
        category TEXT DEFAULT '',
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (auction_id) REFERENCES auctions (auction_id)
    )
    """
    
    CREATE_BIDS_TABLE = """
    CREATE TABLE IF NOT EXISTS bids (
        bid_id INTEGER PRIMARY KEY AUTOINCREMENT,
        lot_id INTEGER NOT NULL,
        auction_id INTEGER NOT NULL,
        amount INTEGER NOT NULL,
        bid_date TEXT NOT NULL,
        bid_time TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        fetched_at TEXT NOT NULL,
        bidder_id TEXT,
        FOREIGN KEY (lot_id) REFERENCES lots (lot_id),
        FOREIGN KEY (auction_id) REFERENCES auctions (auction_id)
    )
    """
    
    CREATE_LIFECYCLE_LOG_TABLE = """
    CREATE TABLE IF NOT EXISTS lifecycle_log (
        log_id INTEGER PRIMARY KEY AUTOINCREMENT,
        auction_id INTEGER,
        lot_id INTEGER,
        action TEXT NOT NULL,
        status TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        details TEXT DEFAULT '',
        FOREIGN KEY (auction_id) REFERENCES auctions (auction_id),
        FOREIGN KEY (lot_id) REFERENCES lots (lot_id)
    )
    """
    
    CREATE_INDEXES = [
        "CREATE INDEX IF NOT EXISTS idx_auctions_status ON auctions (status)",
        "CREATE INDEX IF NOT EXISTS idx_auctions_lifecycle ON auctions (lifecycle_stage)",
        "CREATE INDEX IF NOT EXISTS idx_lots_auction ON lots (auction_id)",
        "CREATE INDEX IF NOT EXISTS idx_lots_status ON lots (status)",
        "CREATE INDEX IF NOT EXISTS idx_lots_closing ON lots (closing_time)",
        "CREATE INDEX IF NOT EXISTS idx_bids_lot ON bids (lot_id)",
        "CREATE INDEX IF NOT EXISTS idx_bids_timestamp ON bids (timestamp)",
        "CREATE INDEX IF NOT EXISTS idx_lifecycle_auction ON lifecycle_log (auction_id)",
        "CREATE INDEX IF NOT EXISTS idx_lifecycle_lot ON lifecycle_log (lot_id)",
        "CREATE INDEX IF NOT EXISTS idx_lifecycle_timestamp ON lifecycle_log (timestamp)"
    ]
    
    @classmethod
    def get_all_create_statements(cls) -> List[str]:
        """Get all table creation statements"""
        return [
            cls.CREATE_AUCTIONS_TABLE,
            cls.CREATE_LOTS_TABLE,
            cls.CREATE_BIDS_TABLE,
            cls.CREATE_LIFECYCLE_LOG_TABLE
        ] + cls.CREATE_INDEXES


def datetime_to_string(dt: Optional[datetime]) -> Optional[str]:
    """Convert datetime to ISO string for database storage"""
    return dt.isoformat() if dt else None


def string_to_datetime(s: Optional[str]) -> Optional[datetime]:
    """Convert ISO string to datetime from database"""
    if not s:
        return None
    try:
        return datetime.fromisoformat(s)
    except (ValueError, TypeError):
        return None


def serialize_images(images: List[str]) -> str:
    """Serialize image list to JSON string"""
    return json.dumps(images) if images else "[]"


def deserialize_images(images_json: str) -> List[str]:
    """Deserialize JSON string to image list"""
    try:
        return json.loads(images_json) if images_json else []
    except (json.JSONDecodeError, TypeError):
        return []
