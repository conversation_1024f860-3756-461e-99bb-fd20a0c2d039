{% extends "base.html" %}

{% block title %}Dashboard - Auction Scraper{% endblock %}

{% block content %}
<div class="col-12">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-speedometer2"></i> Dashboard</h1>
        <button class="btn btn-primary" onclick="triggerAuctionDiscovery()">
            <i class="bi bi-search"></i> Discover Auctions
        </button>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-subtitle mb-2 text-muted">Total Auctions</h6>
                            <h3 class="card-title">{{ stats.total_auctions or 0 }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-hammer fs-1 text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-subtitle mb-2 text-muted">Total Lots</h6>
                            <h3 class="card-title">{{ stats.total_lots or 0 }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-box fs-1 text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-subtitle mb-2 text-muted">Total Bids</h6>
                            <h3 class="card-title">{{ stats.total_bids or 0 }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-currency-euro fs-1 text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-subtitle mb-2 text-muted">Active Lots</h6>
                            <h3 class="card-title">{{ stats.active_lots or 0 }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-play-circle fs-1 text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Auctions -->
        <div class="col-lg-6">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="bi bi-list"></i> Recent Auctions</h5>
                    <a href="{{ url_for('auctions_list') }}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if recent_auctions %}
                        <div class="list-group list-group-flush">
                            {% for auction in recent_auctions %}
                            <div class="list-group-item d-flex justify-content-between align-items-start">
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold">
                                        <a href="{{ url_for('auction_details', auction_id=auction.auction_id) }}" class="text-decoration-none">
                                            {{ auction.title }}
                                        </a>
                                    </div>
                                    <small class="text-muted">
                                        ID: {{ auction.auction_id }} | 
                                        {% if auction.start_time %}
                                            Start: {{ auction.start_time | datetime_format('%m/%d %H:%M') }}
                                        {% else %}
                                            Start: TBD
                                        {% endif %}
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge {{ auction.status | status_badge }} status-badge">{{ auction.status }}</span>
                                    <br>
                                    <small class="text-muted">{{ auction.lot_count }} lots</small>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="bi bi-inbox fs-1"></i>
                            <p>No auctions found. Click "Discover Auctions" to start scraping.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Upcoming Lots -->
        <div class="col-lg-6">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="bi bi-clock"></i> Upcoming Lots (48h)</h5>
                    <a href="{{ url_for('timetable') }}" class="btn btn-sm btn-outline-primary">View Timetable</a>
                </div>
                <div class="card-body">
                    {% if upcoming_lots %}
                        <div class="list-group list-group-flush">
                            {% for lot in upcoming_lots[:10] %}
                            <div class="list-group-item d-flex justify-content-between align-items-start">
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold">
                                        <a href="{{ url_for('lot_details', lot_id=lot.lot_id) }}" class="text-decoration-none">
                                            {{ lot.title[:50] }}{% if lot.title|length > 50 %}...{% endif %}
                                        </a>
                                    </div>
                                    <small class="text-muted">
                                        Lot {{ lot.lot_number }} | 
                                        {% if lot.closing_time %}
                                            Closes: {{ lot.closing_time | datetime_format('%m/%d %H:%M') }}
                                        {% else %}
                                            Closing: TBD
                                        {% endif %}
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge {{ lot.status | status_badge }} status-badge">{{ lot.status }}</span>
                                    <br>
                                    <small class="text-muted">{{ lot.current_price | currency }}</small>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="bi bi-calendar-x fs-1"></i>
                            <p>No upcoming lots found.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Auction Tree Navigation -->
        <div class="col-lg-6">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="bi bi-diagram-3"></i> Auction Tree</h5>
                    <a href="{{ url_for('auction_tree') }}" class="btn btn-sm btn-outline-primary">Full Tree</a>
                </div>
                <div class="card-body auction-tree">
                    {% if auction_tree %}
                        <div class="list-group list-group-flush">
                            {% for auction in auction_tree[:8] %}
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-folder"></i>
                                        <a href="{{ url_for('auction_details', auction_id=auction.auction_id) }}" class="text-decoration-none ms-2">
                                            {{ auction.title[:40] }}{% if auction.title|length > 40 %}...{% endif %}
                                        </a>
                                    </div>
                                    <div>
                                        <span class="badge bg-secondary">{{ auction.actual_lots }} lots</span>
                                        <button class="btn btn-sm btn-outline-primary ms-1" onclick="triggerLotDiscovery({{ auction.auction_id }})">
                                            <i class="bi bi-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="bi bi-tree fs-1"></i>
                            <p>No auction tree data available.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Activity Log -->
        <div class="col-lg-6">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="bi bi-activity"></i> Recent Activity</h5>
                    <a href="{{ url_for('scraping_control') }}" class="btn btn-sm btn-outline-primary">Control Panel</a>
                </div>
                <div class="card-body">
                    {% if recent_logs %}
                        <div class="list-group list-group-flush">
                            {% for log in recent_logs[:10] %}
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold">{{ log.action.replace('_', ' ').title() }}</div>
                                        <small class="text-muted">
                                            {% if log.auction_id %}Auction {{ log.auction_id }}{% endif %}
                                            {% if log.lot_id %}Lot {{ log.lot_id }}{% endif %}
                                            {% if log.details %} - {{ log.details }}{% endif %}
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge {{ log.status | status_badge }} status-badge">{{ log.status }}</span>
                                        <br>
                                        <small class="text-muted">{{ log.timestamp | time_ago }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="bi bi-journal-x fs-1"></i>
                            <p>No recent activity.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Dashboard-specific JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        // Add closing soon animation to lots closing within 1 hour
        const now = new Date();
        const oneHourFromNow = new Date(now.getTime() + 60 * 60 * 1000);
        
        document.querySelectorAll('[data-closing-time]').forEach(element => {
            const closingTime = new Date(element.dataset.closingTime);
            if (closingTime <= oneHourFromNow && closingTime > now) {
                element.classList.add('closing-soon');
            }
        });
    });
</script>
{% endblock %}
