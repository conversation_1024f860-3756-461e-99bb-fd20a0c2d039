#!/usr/bin/env python3
"""
Aurena Auction Web Interface - Clean, Streamlined Version
Provides a web dashboard to view auction and lot data.
"""

from flask import Flask, render_template, jsonify, request
import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional

app = Flask(__name__)

class AuctionDatabase:
    """Database interface for auction data"""
    
    def __init__(self, db_path: str = "aurena_auctions.db"):
        self.db_path = db_path
    
    def get_auctions(self, limit: int = 50) -> List[Dict]:
        """Get list of auctions"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # Use the actual column names from the new database schema
                cursor.execute("""
                    SELECT auction_id, title, start_time, end_time,
                           status, lot_count, location
                    FROM auctions
                    ORDER BY start_time DESC
                    LIMIT ?
                """, (limit,))

                auctions = []
                for row in cursor.fetchall():
                    auction = dict(row)
                    # Parse datetime strings
                    auction['start_time'] = datetime.fromisoformat(auction['start_time'])
                    auction['end_time'] = datetime.fromisoformat(auction['end_time'])
                    # Status is already a string in the new schema
                    auctions.append(auction)

                return auctions

        except Exception as e:
            print(f"Error fetching auctions: {e}")
            return []
    
    def get_auction_details(self, auction_id: int) -> Optional[Dict]:
        """Get detailed auction information"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT auction_id, title, start_time, end_time,
                           status, lot_count, location
                    FROM auctions WHERE auction_id = ?
                """, (auction_id,))

                row = cursor.fetchone()
                if row:
                    auction = dict(row)
                    auction['start_time'] = datetime.fromisoformat(auction['start_time'])
                    auction['end_time'] = datetime.fromisoformat(auction['end_time'])
                    # Status is already a string in the new schema
                    return auction

                return None

        except Exception as e:
            print(f"Error fetching auction details: {e}")
            return None
    
    def get_lots_by_auction(self, auction_id: int) -> List[Dict]:
        """Get lots for a specific auction"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT lot_id, lot_number, title, description, starting_price, 
                           current_price, closing_time, status, bid_count, images
                    FROM lots 
                    WHERE auction_id = ? 
                    ORDER BY lot_number
                """, (auction_id,))
                
                lots = []
                for row in cursor.fetchall():
                    lot = dict(row)
                    # Parse datetime and JSON fields
                    if lot['closing_time']:
                        lot['closing_time'] = datetime.fromisoformat(lot['closing_time'])
                    lot['images'] = json.loads(lot['images']) if lot['images'] else []
                    lots.append(lot)
                
                return lots
                
        except Exception as e:
            print(f"Error fetching lots: {e}")
            return []
    
    def get_upcoming_lots(self, hours: int = 24) -> List[Dict]:
        """Get lots closing within specified hours"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                future_time = (datetime.now() + timedelta(hours=hours)).isoformat()
                current_time = datetime.now().isoformat()
                
                cursor.execute("""
                    SELECT l.lot_id, l.lot_number, l.title, l.current_price, 
                           l.closing_time, l.auction_id, a.title as auction_title
                    FROM lots l
                    JOIN auctions a ON l.auction_id = a.auction_id
                    WHERE l.closing_time BETWEEN ? AND ?
                    ORDER BY l.closing_time
                """, (current_time, future_time))
                
                lots = []
                for row in cursor.fetchall():
                    lot = dict(row)
                    lot['closing_time'] = datetime.fromisoformat(lot['closing_time'])
                    lots.append(lot)
                
                return lots
                
        except Exception as e:
            print(f"Error fetching upcoming lots: {e}")
            return []
    
    def get_statistics(self) -> Dict:
        """Get database statistics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Get basic counts
                cursor.execute("SELECT COUNT(*) FROM auctions")
                total_auctions = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(*) FROM lots")
                total_lots = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(*) FROM lots WHERE current_price > 0")
                lots_with_bids = cursor.fetchone()[0]

                # Get upcoming lots
                tomorrow = (datetime.now() + timedelta(days=1)).isoformat()
                cursor.execute("SELECT COUNT(*) FROM lots WHERE closing_time <= ? AND closing_time > ?",
                             (tomorrow, datetime.now().isoformat()))
                upcoming_lots = cursor.fetchone()[0]

                # Get active auctions (use status = 'active' for active)
                cursor.execute("SELECT COUNT(*) FROM auctions WHERE status = 'active'")
                active_auctions = cursor.fetchone()[0]

                return {
                    'total_auctions': total_auctions,
                    'active_auctions': active_auctions,
                    'total_lots': total_lots,
                    'lots_with_bids': lots_with_bids,
                    'upcoming_lots': upcoming_lots
                }

        except Exception as e:
            print(f"Error getting statistics: {e}")
            return {}

    def get_auction_tree(self) -> List[Dict]:
        """Get auction tree data for navigation"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # Get auctions with lot counts
                cursor.execute("""
                    SELECT a.auction_id, a.title, a.start_time, a.end_time,
                           a.status, COUNT(l.lot_id) as lot_count,
                           SUM(CASE WHEN l.current_price > 0 THEN 1 ELSE 0 END) as lots_with_bids
                    FROM auctions a
                    LEFT JOIN lots l ON a.auction_id = l.auction_id
                    GROUP BY a.auction_id
                    ORDER BY a.start_time DESC
                    LIMIT 20
                """)

                auctions = []
                for row in cursor.fetchall():
                    auction = dict(row)
                    auction['start_time'] = datetime.fromisoformat(auction['start_time'])
                    auction['end_time'] = datetime.fromisoformat(auction['end_time'])
                    # Status is already a string in the new schema

                    # Determine auction status for timetable
                    now = datetime.now()
                    if auction['start_time'] > now:
                        auction['timetable_status'] = 'Waiting'
                    elif auction['start_time'] <= now <= auction['end_time']:
                        auction['timetable_status'] = 'Active'
                    else:
                        auction['timetable_status'] = 'Closed'

                    auctions.append(auction)

                return auctions

        except Exception as e:
            print(f"Error getting auction tree: {e}")
            return []

    def get_lot_bids(self, lot_id: int) -> List[Dict]:
        """Get real bid history for a specific lot"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # First try to get real bids from the new table
                cursor.execute("""
                    SELECT bid_id, amount, bid_date, bid_time, timestamp, fetched_at
                    FROM real_bids
                    WHERE lot_id = ?
                    ORDER BY timestamp DESC
                """, (lot_id,))

                real_bids = cursor.fetchall()

                if real_bids:
                    # Use real bid data
                    bids = []
                    for row in real_bids:
                        bid = dict(row)
                        bid['timestamp'] = datetime.fromisoformat(bid['timestamp'])
                        bid['fetched_at'] = datetime.fromisoformat(bid['fetched_at'])
                        bid['amount_euros'] = bid['amount'] // 100
                        bid['is_real'] = True
                        bids.append(bid)
                    return bids
                else:
                    # Fallback to old bid table if no real bids
                    cursor.execute("""
                        SELECT bid_id, bidder_id, amount, timestamp, bid_type, status
                        FROM bids
                        WHERE lot_id = ?
                        ORDER BY timestamp DESC
                    """, (lot_id,))

                    bids = []
                    for row in cursor.fetchall():
                        bid = dict(row)
                        if bid['timestamp']:
                            bid['timestamp'] = datetime.fromisoformat(bid['timestamp'])
                        bid['amount_euros'] = (bid['amount'] or 0) // 100
                        bid['is_real'] = False
                        bids.append(bid)

                    return bids

        except Exception as e:
            print(f"Error getting lot bids: {e}")
            return []

    def get_timetable_data(self, hours: int = 48) -> List[Dict]:
        """Get timetable data showing lots closing within specified hours"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                future_time = (datetime.now() + timedelta(hours=hours)).isoformat()
                current_time = datetime.now().isoformat()

                cursor.execute("""
                    SELECT l.lot_id, l.lot_number, l.title, l.current_price,
                           l.closing_time, l.status, l.auction_id, a.title as auction_title
                    FROM lots l
                    JOIN auctions a ON l.auction_id = a.auction_id
                    WHERE l.closing_time BETWEEN ? AND ?
                    ORDER BY l.closing_time
                """, (current_time, future_time))

                lots = []
                for row in cursor.fetchall():
                    lot = dict(row)
                    lot['closing_time'] = datetime.fromisoformat(lot['closing_time'])

                    # Determine lot status for timetable
                    now = datetime.now()
                    time_to_close = (lot['closing_time'] - now).total_seconds()

                    if time_to_close > 3600:  # More than 1 hour
                        lot['timetable_status'] = 'Waiting'
                        lot['status_class'] = 'info'
                    elif time_to_close > 300:  # More than 5 minutes
                        lot['timetable_status'] = 'Closing Soon'
                        lot['status_class'] = 'warning'
                    elif time_to_close > 0:  # Less than 5 minutes
                        lot['timetable_status'] = 'Critical'
                        lot['status_class'] = 'danger'
                    else:
                        lot['timetable_status'] = 'Closed'
                        lot['status_class'] = 'secondary'

                    lots.append(lot)

                return lots

        except Exception as e:
            print(f"Error getting timetable data: {e}")
            return []

    def get_auction_lots(self, auction_id: int) -> List[Dict]:
        """Get lots for a specific auction"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT lot_id, lot_number, title, description, current_price,
                           closing_time, status, bid_count
                    FROM lots
                    WHERE auction_id = ?
                    ORDER BY lot_number
                """, (auction_id,))

                lots = []
                for row in cursor.fetchall():
                    lot = dict(row)
                    if lot['closing_time']:
                        lot['closing_time'] = datetime.fromisoformat(lot['closing_time'])
                    lots.append(lot)

                return lots

        except Exception as e:
            print(f"Error getting auction lots: {e}")
            return []

# Initialize database interface
db = AuctionDatabase()

@app.route('/')
def dashboard():
    """Main dashboard page with tree navigation and timetable"""
    stats = db.get_statistics()
    recent_auctions = db.get_auctions(limit=10)
    upcoming_lots = db.get_upcoming_lots(hours=48)  # Extended to 48 hours

    # Get auction tree data for navigation
    auction_tree = db.get_auction_tree()

    return render_template('dashboard.html',
                         stats=stats,
                         auctions=recent_auctions,
                         upcoming_lots=upcoming_lots,
                         auction_tree=auction_tree)

@app.route('/auctions')
def auctions_list():
    """Auctions list page"""
    auctions = db.get_auctions(limit=100)
    return render_template('auctions.html', auctions=auctions)

@app.route('/auction/<int:auction_id>')
def auction_details(auction_id):
    """Auction details page"""
    auction = db.get_auction_details(auction_id)
    if not auction:
        return "Auction not found", 404
    
    lots = db.get_lots_by_auction(auction_id)
    return render_template('auction_details.html', auction=auction, lots=lots)

@app.route('/lot/<int:lot_id>')
def lot_details(lot_id):
    """Lot details page with bid history"""
    try:
        with sqlite3.connect(db.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute("""
                SELECT l.*, a.title as auction_title
                FROM lots l
                JOIN auctions a ON l.auction_id = a.auction_id
                WHERE l.lot_id = ?
            """, (lot_id,))

            row = cursor.fetchone()
            if row:
                lot = dict(row)
                if lot['closing_time']:
                    lot['closing_time'] = datetime.fromisoformat(lot['closing_time'])
                lot['images'] = json.loads(lot['images']) if lot['images'] else []

                # Get bid history
                bids = db.get_lot_bids(lot_id)

                return render_template('lot_details.html', lot=lot, bids=bids)

            return "Lot not found", 404

    except Exception as e:
        print(f"Error fetching lot details: {e}")
        return "Error loading lot", 500

@app.route('/timetable')
def timetable():
    """Timetable page showing lots closing soon"""
    hours = request.args.get('hours', 48, type=int)
    timetable_data = db.get_timetable_data(hours=hours)

    return render_template('timetable.html',
                         timetable_data=timetable_data,
                         hours=hours)

@app.route('/auction-tree')
def auction_tree():
    """Auction tree navigation page"""
    auction_tree = db.get_auction_tree()
    return render_template('auction_tree.html', auction_tree=auction_tree)

@app.route('/api/lots/<int:auction_id>')
def api_auction_lots(auction_id):
    """API endpoint to get lots for an auction"""
    try:
        lots = db.get_auction_lots(auction_id)
        return jsonify(lots)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/fetch-bids/<int:lot_id>')
def fetch_lot_bids(lot_id):
    """Manually fetch bid history for a lot"""
    try:
        from bid_fetcher import BidFetcher

        # Get lot details
        with sqlite3.connect(db.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute("SELECT auction_id, title, closing_time FROM lots WHERE lot_id = ?", (lot_id,))
            lot_data = cursor.fetchone()

        if not lot_data:
            return jsonify({'success': False, 'message': 'Lot not found in database'}), 404

        # Check if lot is likely to still be available
        from datetime import datetime, timedelta
        closing_time = datetime.fromisoformat(lot_data['closing_time'])
        now = datetime.now()
        hours_since_close = (now - closing_time).total_seconds() / 3600

        if hours_since_close > 24:
            return jsonify({
                'success': False,
                'message': f'Lot closed {hours_since_close:.1f} hours ago. Bid data may no longer be available on Aurena.'
            })

        # Fetch bid history
        fetcher = BidFetcher(db.db_path)
        bids = fetcher.fetch_lot_bid_history(lot_id, lot_data['title'])

        if bids:
            # Save the bids
            success = fetcher.save_bid_history(lot_id, lot_data['auction_id'], bids)
            if success:
                return jsonify({
                    'success': True,
                    'message': f'Successfully fetched {len(bids)} bids for lot {lot_id}',
                    'bid_count': len(bids)
                })
            else:
                return jsonify({'success': False, 'message': 'Failed to save bid history to database'})
        else:
            # Check if it's a 410 error (lot removed) or other issue
            return jsonify({
                'success': False,
                'message': 'No bid history found. Lot may have been removed from Aurena or has no bids.'
            })

    except Exception as e:
        error_msg = str(e)
        if "410" in error_msg:
            return jsonify({
                'success': False,
                'message': 'Lot has been removed from Aurena (HTTP 410). This is normal for closed auctions.'
            })
        else:
            return jsonify({'success': False, 'message': f'Error: {error_msg}'})

@app.route('/api/stats')
def api_stats():
    """API endpoint for statistics"""
    return jsonify(db.get_statistics())

@app.route('/api/auctions')
def api_auctions():
    """API endpoint for auctions"""
    limit = request.args.get('limit', 50, type=int)
    auctions = db.get_auctions(limit=limit)
    
    # Convert datetime objects to strings for JSON serialization
    for auction in auctions:
        auction['start_time'] = auction['start_time'].isoformat()
        auction['end_time'] = auction['end_time'].isoformat()
    
    return jsonify(auctions)

@app.route('/api/lots/<int:auction_id>')
def api_lots(auction_id):
    """API endpoint for lots by auction"""
    lots = db.get_lots_by_auction(auction_id)
    
    # Convert datetime objects to strings for JSON serialization
    for lot in lots:
        if lot['closing_time']:
            lot['closing_time'] = lot['closing_time'].isoformat()
    
    return jsonify(lots)

@app.route('/api/upcoming')
def api_upcoming():
    """API endpoint for upcoming lots"""
    hours = request.args.get('hours', 24, type=int)
    lots = db.get_upcoming_lots(hours=hours)
    
    # Convert datetime objects to strings for JSON serialization
    for lot in lots:
        lot['closing_time'] = lot['closing_time'].isoformat()
    
    return jsonify(lots)

# Scraping Control Routes
@app.route('/scraping-control')
def scraping_control():
    """Scraping control dashboard"""
    try:
        # Get system status
        with sqlite3.connect(db.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Get lifecycle statistics
            cursor.execute("""
                SELECT lifecycle_stage, COUNT(*) as count
                FROM auctions
                GROUP BY lifecycle_stage
            """)
            auction_stages = dict(cursor.fetchall())

            cursor.execute("""
                SELECT lifecycle_stage, COUNT(*) as count
                FROM lots
                GROUP BY lifecycle_stage
            """)
            lot_stages = dict(cursor.fetchall())

            # Get recent lifecycle actions
            cursor.execute("""
                SELECT * FROM lifecycle_log
                ORDER BY timestamp DESC
                LIMIT 20
            """)
            recent_actions = [dict(row) for row in cursor.fetchall()]

            # Get scraping statistics
            cursor.execute("SELECT COUNT(*) FROM auctions")
            total_auctions = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM lots")
            total_lots = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM real_bids")
            total_bids = cursor.fetchone()[0]

        return render_template('scraping_control.html',
                             auction_stages=auction_stages,
                             lot_stages=lot_stages,
                             recent_actions=recent_actions,
                             total_auctions=total_auctions,
                             total_lots=total_lots,
                             total_bids=total_bids)

    except Exception as e:
        return f"Error loading scraping control: {e}", 500

@app.route('/api/discover-auctions', methods=['POST'])
def api_discover_auctions():
    """API endpoint to trigger auction discovery (Step 1) - NO HARDCODED DATA"""
    try:
        from dynamic_lifecycle_system import DynamicLifecycleSystem

        system = DynamicLifecycleSystem(db.db_path)
        auctions = system.step1_discover_auctions()

        return jsonify({
            'success': True,
            'message': f'Dynamically discovered {len(auctions)} auctions (NO hardcoded data)',
            'auctions': auctions
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500

@app.route('/api/fetch-lots/<int:auction_id>', methods=['POST'])
def api_fetch_lots(auction_id):
    """API endpoint to fetch lots for an auction (Step 2) - Dynamic extraction"""
    try:
        from dynamic_lifecycle_system import DynamicLifecycleSystem

        reason = request.json.get('reason', 'dashboard_request') if request.json else 'dashboard_request'

        system = DynamicLifecycleSystem(db.db_path)
        lots = system.step2_fetch_auction_lots(auction_id, reason)

        return jsonify({
            'success': True,
            'message': f'Dynamically fetched {len(lots)} lots for auction {auction_id}',
            'lots': lots
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500

@app.route('/api/fetch-bids/<int:lot_id>', methods=['POST'])
def api_fetch_bids(lot_id):
    """API endpoint to fetch bids for a lot (Step 3) - Real bid extraction"""
    try:
        from dynamic_lifecycle_system import DynamicLifecycleSystem

        reason = request.json.get('reason', 'dashboard_request') if request.json else 'dashboard_request'

        system = DynamicLifecycleSystem(db.db_path)
        bids = system.step3_fetch_lot_bids(lot_id, reason)

        return jsonify({
            'success': True,
            'message': f'Fetched {len(bids)} real bids for lot {lot_id}',
            'bids': bids
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500

if __name__ == '__main__':
    print("Starting Aurena Auction Web Interface...")
    print("Access the dashboard at: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
